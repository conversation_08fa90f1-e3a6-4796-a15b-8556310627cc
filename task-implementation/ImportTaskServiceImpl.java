package org.jeecg.modules.reg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.entity.ImportTask;
import org.jeecg.modules.reg.mapper.ImportTaskMapper;
import org.jeecg.modules.reg.mapper.ImportTaskMapper.ImportTaskStatistics;
import org.jeecg.modules.reg.service.IImportTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @Description: 导入任务服务实现
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class ImportTaskServiceImpl extends ServiceImpl<ImportTaskMapper, ImportTask> implements IImportTaskService {

    @Autowired
    private ImportTaskMapper importTaskMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createImportTask(String taskName, String taskType, String fileName, 
                                  Long fileSize, String companyRegId, String companyName, String createBy) {
        try {
            String taskId = UUID.randomUUID().toString();
            
            ImportTask importTask = new ImportTask();
            importTask.setId(taskId);
            importTask.setTaskName(taskName);
            importTask.setTaskType(taskType);
            importTask.setStatus(ImportTask.TaskStatus.PENDING.getCode());
            importTask.setProgress(0);
            importTask.setTotalCount(0);
            importTask.setProcessedCount(0);
            importTask.setSuccessCount(0);
            importTask.setFailureCount(0);
            importTask.setCurrentMessage("任务已创建，等待处理");
            importTask.setFileName(fileName);
            importTask.setFileSize(fileSize);
            importTask.setCompanyRegId(companyRegId);
            importTask.setCompanyName(companyName);
            importTask.setCreateBy(createBy);
            importTask.setCreateTime(new Date());
            
            boolean success = this.save(importTask);
            if (success) {
                log.info("创建导入任务成功: taskId={}, fileName={}, companyRegId={}", 
                        taskId, fileName, companyRegId);
                return taskId;
            } else {
                log.error("创建导入任务失败: fileName={}, companyRegId={}", fileName, companyRegId);
                return null;
            }
        } catch (Exception e) {
            log.error("创建导入任务异常: fileName={}, companyRegId={}", fileName, companyRegId, e);
            throw new RuntimeException("创建导入任务失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startTask(String taskId, Integer totalCount) {
        try {
            ImportTask importTask = this.getById(taskId);
            if (importTask == null) {
                log.error("任务不存在: taskId={}", taskId);
                return false;
            }
            
            if (!ImportTask.TaskStatus.PENDING.getCode().equals(importTask.getStatus())) {
                log.error("任务状态不正确，无法启动: taskId={}, status={}", taskId, importTask.getStatus());
                return false;
            }
            
            importTask.setStatus(ImportTask.TaskStatus.PROCESSING.getCode());
            importTask.setTotalCount(totalCount);
            importTask.setStartTime(new Date());
            importTask.setCurrentMessage("开始处理导入任务");
            importTask.setUpdateTime(new Date());
            
            boolean success = this.updateById(importTask);
            if (success) {
                log.info("启动导入任务成功: taskId={}, totalCount={}", taskId, totalCount);
            } else {
                log.error("启动导入任务失败: taskId={}", taskId);
            }
            return success;
        } catch (Exception e) {
            log.error("启动导入任务异常: taskId={}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskProgress(String taskId, Integer processedCount, Integer successCount, 
                                     Integer failureCount, String currentMessage) {
        try {
            int progress = 0;
            ImportTask importTask = this.getById(taskId);
            if (importTask != null && importTask.getTotalCount() != null && importTask.getTotalCount() > 0) {
                progress = (int) ((double) processedCount / importTask.getTotalCount() * 100);
            }
            
            int result = importTaskMapper.updateProgress(taskId, progress, processedCount, 
                                                       successCount, failureCount, currentMessage);
            
            if (result > 0) {
                log.debug("更新任务进度成功: taskId={}, progress={}%, processed={}/{}", 
                         taskId, progress, processedCount, importTask != null ? importTask.getTotalCount() : 0);
            } else {
                log.warn("更新任务进度失败: taskId={}", taskId);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("更新任务进度异常: taskId={}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeTask(String taskId, Integer successCount, Integer failureCount, String resultData) {
        try {
            ImportTask importTask = this.getById(taskId);
            if (importTask == null) {
                log.error("任务不存在: taskId={}", taskId);
                return false;
            }
            
            importTask.setStatus(ImportTask.TaskStatus.COMPLETED.getCode());
            importTask.setSuccessCount(successCount);
            importTask.setFailureCount(failureCount);
            importTask.setProcessedCount(successCount + failureCount);
            importTask.setProgress(100);
            importTask.setCurrentMessage("导入任务完成");
            importTask.setEndTime(new Date());
            importTask.setResultData(resultData);
            importTask.calculateDuration();
            importTask.setUpdateTime(new Date());
            
            boolean success = this.updateById(importTask);
            if (success) {
                log.info("完成导入任务: taskId={}, success={}, failure={}", taskId, successCount, failureCount);
            } else {
                log.error("完成导入任务失败: taskId={}", taskId);
            }
            return success;
        } catch (Exception e) {
            log.error("完成导入任务异常: taskId={}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean failTask(String taskId, String errorMessage) {
        try {
            int result = importTaskMapper.updateStatus(taskId, ImportTask.TaskStatus.FAILED.getCode(), errorMessage);
            
            if (result > 0) {
                log.info("标记任务失败: taskId={}, error={}", taskId, errorMessage);
            } else {
                log.error("标记任务失败操作失败: taskId={}", taskId);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("标记任务失败异常: taskId={}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelTask(String taskId) {
        try {
            ImportTask importTask = this.getById(taskId);
            if (importTask == null) {
                log.error("任务不存在: taskId={}", taskId);
                return false;
            }
            
            if (!canCancelTask(taskId)) {
                log.error("任务状态不允许取消: taskId={}, status={}", taskId, importTask.getStatus());
                return false;
            }
            
            int result = importTaskMapper.updateStatus(taskId, ImportTask.TaskStatus.CANCELLED.getCode(), "用户取消");
            
            if (result > 0) {
                log.info("取消任务成功: taskId={}", taskId);
            } else {
                log.error("取消任务失败: taskId={}", taskId);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("取消任务异常: taskId={}", taskId, e);
            return false;
        }
    }

    @Override
    public List<ImportTask> getTasksByStatus(String status) {
        return importTaskMapper.selectByStatus(status);
    }

    @Override
    public List<ImportTask> getTasksByCompanyRegId(String companyRegId) {
        return importTaskMapper.selectByCompanyRegId(companyRegId);
    }

    @Override
    public List<ImportTask> getTasksByCreateBy(String createBy) {
        return importTaskMapper.selectByCreateBy(createBy);
    }

    @Override
    public boolean hasProcessingTask(String companyRegId) {
        return getProcessingTaskCount(companyRegId) > 0;
    }

    @Override
    public int getProcessingTaskCount(String companyRegId) {
        return importTaskMapper.countProcessingTasks(companyRegId);
    }

    @Override
    public ImportTaskStatistics getTaskStatistics(String companyRegId, String startTime, String endTime) {
        return importTaskMapper.getTaskStatistics(companyRegId, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupExpiredTasks(int days) {
        try {
            int deletedCount = importTaskMapper.deleteExpiredTasks(days);
            log.info("清理过期任务完成: 删除{}个任务, 保留天数={}", deletedCount, days);
            return deletedCount;
        } catch (Exception e) {
            log.error("清理过期任务异常: days={}", days, e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String retryFailedTask(String taskId, String createBy) {
        try {
            ImportTask originalTask = this.getById(taskId);
            if (originalTask == null || !canRetryTask(taskId)) {
                log.error("任务不存在或不允许重试: taskId={}", taskId);
                return null;
            }
            
            // 创建新的重试任务
            String newTaskId = createImportTask(
                originalTask.getTaskName() + " (重试)",
                originalTask.getTaskType(),
                originalTask.getFileName(),
                originalTask.getFileSize(),
                originalTask.getCompanyRegId(),
                originalTask.getCompanyName(),
                createBy
            );
            
            if (newTaskId != null) {
                log.info("创建重试任务成功: originalTaskId={}, newTaskId={}", taskId, newTaskId);
            }
            return newTaskId;
        } catch (Exception e) {
            log.error("重试任务异常: taskId={}", taskId, e);
            return null;
        }
    }

    @Override
    public ImportTask getTaskDetail(String taskId) {
        return this.getById(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateTaskStatus(String oldStatus, String newStatus, String errorMessage) {
        try {
            LambdaQueryWrapper<ImportTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ImportTask::getStatus, oldStatus);
            
            List<ImportTask> tasks = this.list(queryWrapper);
            int updateCount = 0;
            
            for (ImportTask task : tasks) {
                int result = importTaskMapper.updateStatus(task.getId(), newStatus, errorMessage);
                if (result > 0) {
                    updateCount++;
                }
            }
            
            log.info("批量更新任务状态完成: {}→{}, 更新{}个任务", oldStatus, newStatus, updateCount);
            return updateCount;
        } catch (Exception e) {
            log.error("批量更新任务状态异常: {}→{}", oldStatus, newStatus, e);
            return 0;
        }
    }

    @Override
    public boolean canCancelTask(String taskId) {
        ImportTask task = this.getById(taskId);
        return task != null && (ImportTask.TaskStatus.PENDING.getCode().equals(task.getStatus()) ||
                               ImportTask.TaskStatus.PROCESSING.getCode().equals(task.getStatus()));
    }

    @Override
    public boolean canRetryTask(String taskId) {
        ImportTask task = this.getById(taskId);
        return task != null && ImportTask.TaskStatus.FAILED.getCode().equals(task.getStatus());
    }

    @Override
    public List<ImportTask> getUserTaskHistory(String createBy, int limit) {
        LambdaQueryWrapper<ImportTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImportTask::getCreateBy, createBy)
                   .orderByDesc(ImportTask::getCreateTime)
                   .last("LIMIT " + limit);
        return this.list(queryWrapper);
    }

    @Override
    public List<ImportTask> getCompanyTaskHistory(String companyRegId, int limit) {
        LambdaQueryWrapper<ImportTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImportTask::getCompanyRegId, companyRegId)
                   .orderByDesc(ImportTask::getCreateTime)
                   .last("LIMIT " + limit);
        return this.list(queryWrapper);
    }
}
