package org.jeecg.modules.reg.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 导入进度信息
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ImportProgressInfo对象", description="导入进度信息")
public class ImportProgressInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private String taskId;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private String status;

    /**
     * 进度百分比（0-100）
     */
    @ApiModelProperty(value = "进度百分比")
    private Integer progress;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    /**
     * 已处理记录数
     */
    @ApiModelProperty(value = "已处理记录数")
    private Integer processedCount;

    /**
     * 成功记录数
     */
    @ApiModelProperty(value = "成功记录数")
    private Integer successCount;

    /**
     * 失败记录数
     */
    @ApiModelProperty(value = "失败记录数")
    private Integer failureCount;

    /**
     * 当前处理信息
     */
    @ApiModelProperty(value = "当前处理信息")
    private String currentMessage;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 开始时间（时间戳）
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    /**
     * 结束时间（时间戳）
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    /**
     * 耗时（毫秒）
     */
    @ApiModelProperty(value = "耗时")
    private Long duration;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 扩展信息（JSON格式）
     */
    @ApiModelProperty(value = "扩展信息")
    private String extraInfo;

    /**
     * 构造函数
     */
    public ImportProgressInfo() {
        this.startTime = System.currentTimeMillis();
        this.progress = 0;
        this.processedCount = 0;
        this.successCount = 0;
        this.failureCount = 0;
    }

    /**
     * 构造函数
     */
    public ImportProgressInfo(String taskId, String status) {
        this();
        this.taskId = taskId;
        this.status = status;
    }

    /**
     * 计算进度百分比
     */
    public void calculateProgress() {
        if (totalCount != null && totalCount > 0 && processedCount != null) {
            this.progress = (int) ((double) processedCount / totalCount * 100);
            // 确保进度不超过100%
            if (this.progress > 100) {
                this.progress = 100;
            }
        } else {
            this.progress = 0;
        }
    }

    /**
     * 计算耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = endTime - startTime;
        }
    }

    /**
     * 获取进度百分比（小数形式）
     */
    public double getProgressPercentage() {
        return totalCount != null && totalCount > 0 ? (double) processedCount / totalCount : 0.0;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        return processedCount != null && processedCount > 0 ? (double) successCount / processedCount : 0.0;
    }

    /**
     * 获取失败率
     */
    public double getFailureRate() {
        return processedCount != null && processedCount > 0 ? (double) failureCount / processedCount : 0.0;
    }

    /**
     * 是否正在处理中
     */
    public boolean isProcessing() {
        return ImportTask.TaskStatus.PROCESSING.getCode().equals(this.status);
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return ImportTask.TaskStatus.COMPLETED.getCode().equals(this.status);
    }

    /**
     * 是否失败
     */
    public boolean isFailed() {
        return ImportTask.TaskStatus.FAILED.getCode().equals(this.status);
    }

    /**
     * 是否已取消
     */
    public boolean isCancelled() {
        return ImportTask.TaskStatus.CANCELLED.getCode().equals(this.status);
    }

    /**
     * 更新处理进度
     */
    public void updateProgress(Integer processedCount, Integer successCount, Integer failureCount, String currentMessage) {
        this.processedCount = processedCount;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.currentMessage = currentMessage;
        calculateProgress();
    }

    /**
     * 完成处理
     */
    public void complete(Integer successCount, Integer failureCount, String message) {
        this.status = ImportTask.TaskStatus.COMPLETED.getCode();
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.processedCount = successCount + failureCount;
        this.progress = 100;
        this.currentMessage = message;
        this.endTime = System.currentTimeMillis();
        calculateDuration();
    }

    /**
     * 标记失败
     */
    public void fail(String errorMessage) {
        this.status = ImportTask.TaskStatus.FAILED.getCode();
        this.errorMessage = errorMessage;
        this.endTime = System.currentTimeMillis();
        calculateDuration();
    }

    /**
     * 标记取消
     */
    public void cancel(String message) {
        this.status = ImportTask.TaskStatus.CANCELLED.getCode();
        this.currentMessage = message;
        this.endTime = System.currentTimeMillis();
        calculateDuration();
    }

    /**
     * 从ImportTask创建进度信息
     */
    public static ImportProgressInfo fromImportTask(ImportTask importTask) {
        if (importTask == null) {
            return null;
        }

        ImportProgressInfo progressInfo = new ImportProgressInfo();
        progressInfo.setTaskId(importTask.getId());
        progressInfo.setStatus(importTask.getStatus());
        progressInfo.setProgress(importTask.getProgress());
        progressInfo.setTotalCount(importTask.getTotalCount());
        progressInfo.setProcessedCount(importTask.getProcessedCount());
        progressInfo.setSuccessCount(importTask.getSuccessCount());
        progressInfo.setFailureCount(importTask.getFailureCount());
        progressInfo.setCurrentMessage(importTask.getCurrentMessage());
        progressInfo.setErrorMessage(importTask.getErrorMessage());
        progressInfo.setFileName(importTask.getFileName());
        progressInfo.setFileSize(importTask.getFileSize());
        progressInfo.setCompanyName(importTask.getCompanyName());
        progressInfo.setCreateBy(importTask.getCreateBy());

        if (importTask.getStartTime() != null) {
            progressInfo.setStartTime(importTask.getStartTime().getTime());
        }
        if (importTask.getEndTime() != null) {
            progressInfo.setEndTime(importTask.getEndTime().getTime());
        }
        progressInfo.setDuration(importTask.getDuration());

        return progressInfo;
    }

    /**
     * 创建简化的进度信息（用于WebSocket推送）
     */
    public ImportProgressInfo createSimplified() {
        ImportProgressInfo simplified = new ImportProgressInfo();
        simplified.setTaskId(this.taskId);
        simplified.setStatus(this.status);
        simplified.setProgress(this.progress);
        simplified.setTotalCount(this.totalCount);
        simplified.setProcessedCount(this.processedCount);
        simplified.setSuccessCount(this.successCount);
        simplified.setFailureCount(this.failureCount);
        simplified.setCurrentMessage(this.currentMessage);
        simplified.setErrorMessage(this.errorMessage);
        simplified.setDuration(this.duration);
        return simplified;
    }

    @Override
    public String toString() {
        return "ImportProgressInfo{" +
                "taskId='" + taskId + '\'' +
                ", status='" + status + '\'' +
                ", progress=" + progress +
                ", processedCount=" + processedCount +
                ", totalCount=" + totalCount +
                ", successCount=" + successCount +
                ", failureCount=" + failureCount +
                ", currentMessage='" + currentMessage + '\'' +
                '}';
    }
}
