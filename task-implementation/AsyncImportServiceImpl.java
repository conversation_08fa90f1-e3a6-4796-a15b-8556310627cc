package org.jeecg.modules.reg.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.reg.entity.ImportTask;
import org.jeecg.modules.reg.entity.ImportProgressInfo;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.vo.BatchResult;
import org.jeecg.modules.reg.service.AsyncImportService;
import org.jeecg.modules.reg.service.CustomerRegService;
import org.jeecg.modules.reg.service.ImportProgressService;
import org.jeecg.modules.reg.service.IImportTaskService;
import org.jeecg.modules.reg.service.DefaultImportProgressCallback;
import org.jeecg.modules.reg.mapper.CompanyRegMapper;
import org.jeecg.modules.reg.mapper.CompanyTeamMapper;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 异步导入服务实现
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class AsyncImportServiceImpl implements AsyncImportService {

    @Autowired
    private CustomerRegService customerRegService;
    
    @Autowired
    private ImportProgressService importProgressService;
    
    @Autowired
    private IImportTaskService importTaskService;
    
    @Autowired
    private CompanyRegMapper companyRegMapper;
    
    @Autowired
    private CompanyTeamMapper companyTeamMapper;
    
    @Autowired
    private RedissonClient redissonClient;

    // 存储正在执行的任务，用于取消操作
    private final Map<String, Thread> runningTasks = new ConcurrentHashMap<>();
    
    // 存储任务的文件数据（用于重试）
    private final Map<String, byte[]> taskFileData = new ConcurrentHashMap<>();

    @Override
    @Async("importExecutor")
    public void processImportAsync(String taskId, MultipartFile file, String companyRegId, LoginUser sysUser) {
        // 记录当前线程，用于取消操作
        runningTasks.put(taskId, Thread.currentThread());
        
        try {
            log.info("开始异步处理导入任务: taskId={}, fileName={}, companyRegId={}", 
                    taskId, file.getOriginalFilename(), companyRegId);
            
            // 保存文件数据用于重试
            byte[] fileBytes = file.getBytes();
            taskFileData.put(taskId, fileBytes);
            
            // 启动任务
            importTaskService.startTask(taskId, 0);
            
            // 创建进度回调
            DefaultImportProgressCallback callback = new DefaultImportProgressCallback(taskId, importProgressService);
            
            // 获取分布式锁
            String importLockKey = "IMPORT_EXCEL_LOCK:" + companyRegId;
            RLock importLock = redissonClient.getLock(importLockKey);
            
            try {
                // 尝试获取锁，最多等待30秒，锁定10分钟
                boolean lockAcquired = importLock.tryLock(30, 600, TimeUnit.SECONDS);
                if (!lockAcquired) {
                    throw new RuntimeException("系统繁忙，该单位正在进行导入操作，请稍后重试");
                }
                
                log.info("获取导入锁成功: taskId={}, lockKey={}", taskId, importLockKey);
                
                // 执行实际的导入逻辑
                JSONObject result = doAsyncImportExcel(taskId, fileBytes, companyRegId, sysUser, callback);
                
                // 完成任务
                int successCount = result.getIntValue("successCount");
                int failureCount = result.getIntValue("failureCount");
                
                importTaskService.completeTask(taskId, successCount, failureCount, result.toJSONString());
                callback.onComplete(successCount, failureCount, "导入完成");
                
                log.info("异步导入任务完成: taskId={}, success={}, failure={}", 
                        taskId, successCount, failureCount);
                
            } finally {
                // 确保释放锁
                if (importLock.isHeldByCurrentThread()) {
                    importLock.unlock();
                    log.info("释放导入锁: taskId={}, lockKey={}", taskId, importLockKey);
                }
            }
            
        } catch (InterruptedException e) {
            log.info("导入任务被取消: taskId={}", taskId);
            importTaskService.cancelTask(taskId);
            importProgressService.cancelProgress(taskId, "任务被用户取消");
        } catch (Exception e) {
            log.error("异步导入任务失败: taskId={}", taskId, e);
            importTaskService.failTask(taskId, e.getMessage());
            importProgressService.failProgress(taskId, "导入失败: " + e.getMessage());
        } finally {
            runningTasks.remove(taskId);
            // 清理文件数据（保留一段时间用于重试）
            // taskFileData.remove(taskId); // 暂时保留，用于重试功能
        }
    }

    /**
     * 执行实际的异步导入逻辑
     * 基于现有的doImportExcel方法，添加进度回调
     */
    private JSONObject doAsyncImportExcel(String taskId, byte[] fileBytes, String companyRegId, 
                                         LoginUser sysUser, DefaultImportProgressCallback callback) throws Exception {
        
        // 1. 验证单位和分组
        CompanyReg companyReg = companyRegMapper.selectById(companyRegId);
        if (companyReg == null) {
            throw new RuntimeException("单位不存在！");
        }
        
        List<CompanyTeam> companyTeams = companyTeamMapper.selectByMainId(companyReg.getId());
        if (CollectionUtils.isEmpty(companyTeams)) {
            throw new RuntimeException("该单位下未设置分组！");
        }
        
        callback.onProgress(0, 0, 0, "正在解析Excel文件...");
        checkCancellation(taskId);
        
        // 2. 解析Excel文件
        List<CustomerReg> customerRegList;
        try (InputStream inputStream = new ByteArrayInputStream(fileBytes)) {
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            
            customerRegList = ExcelImportUtil.importExcel(inputStream, CustomerReg.class, params);
        }
        
        if (CollectionUtils.isEmpty(customerRegList)) {
            throw new RuntimeException("Excel文件中没有有效数据");
        }
        
        int totalCount = customerRegList.size();
        importTaskService.updateTaskProgress(taskId, 0, 0, 0, 
                String.format("文件解析完成，共 %d 条记录", totalCount));
        callback.onStart(totalCount, String.format("开始处理 %d 条记录", totalCount));
        
        checkCancellation(taskId);
        
        // 3. 批量处理数据（添加进度回调）
        BatchProcessResultWithCallback result = batchProcessCustomerRegsWithCallback(
                customerRegList, companyReg, companyTeams, sysUser, taskId, callback);
        
        checkCancellation(taskId);
        
        // 4. 保存错误记录
        callback.onProgress(totalCount, result.getSuccessCount(), result.getFailureCount(), "正在保存错误记录...");
        
        try {
            // 复用现有的错误记录保存逻辑
            saveImportErrorRecords(companyRegId, result.getFailureResults(), sysUser);
        } catch (Exception e) {
            log.error("保存错误记录失败: taskId={}", taskId, e);
        }
        
        // 5. 构建返回结果
        JSONObject resultJson = new JSONObject();
        resultJson.put("taskId", taskId);
        resultJson.put("totalCount", totalCount);
        resultJson.put("successCount", result.getSuccessCount());
        resultJson.put("failureCount", result.getFailureCount());
        resultJson.put("successList", result.getSuccessList());
        resultJson.put("failureList", result.getFailureResults());
        
        return resultJson;
    }

    /**
     * 带进度回调的批量处理方法
     * 基于现有的batchProcessCustomerRegs方法，添加进度回调
     */
    private BatchProcessResultWithCallback batchProcessCustomerRegsWithCallback(
            List<CustomerReg> customerRegList, CompanyReg companyReg, List<CompanyTeam> companyTeams, 
            LoginUser sysUser, String taskId, DefaultImportProgressCallback callback) {
        
        BatchProcessResultWithCallback result = new BatchProcessResultWithCallback();
        int totalCount = customerRegList.size();
        int processedCount = 0;
        int batchSize = 50; // 每批处理50条记录
        
        try {
            // 预处理：设置基本信息
            for (CustomerReg customerReg : customerRegList) {
                customerReg.setCompanyRegId(companyReg.getId());
                customerReg.setCompanyRegName(companyReg.getRegName());
                customerReg.setCreatorBy(sysUser.getUsername());
            }
            
            // 批量检查重复记录
            callback.onProgress(processedCount, 0, 0, "正在检查重复记录...");
            checkCancellation(taskId);
            
            Map<String, CustomerReg> existingRegsMap = batchCheckExistingRegs(customerRegList, companyReg.getId());
            
            // 分批处理记录
            for (int i = 0; i < customerRegList.size(); i += batchSize) {
                checkCancellation(taskId);
                
                int endIndex = Math.min(i + batchSize, customerRegList.size());
                List<CustomerReg> batch = customerRegList.subList(i, endIndex);
                
                // 处理当前批次
                for (CustomerReg customerReg : batch) {
                    try {
                        String errorMessage = processCustomerRegRecordWithCache(
                                customerReg, companyReg, companyTeams, sysUser, existingRegsMap);
                        
                        if (errorMessage != null) {
                            result.addFailure(customerReg, errorMessage);
                        } else {
                            result.addSuccess(customerReg);
                        }
                        
                        processedCount++;
                        
                        // 每处理10条记录更新一次进度
                        if (processedCount % 10 == 0 || processedCount == totalCount) {
                            String message = String.format("正在处理第 %d/%d 条记录", processedCount, totalCount);
                            callback.onProgress(processedCount, result.getSuccessCount(), 
                                              result.getFailureCount(), message);
                        }
                        
                    } catch (Exception e) {
                        log.error("处理单条记录失败: taskId={}, name={}", taskId, customerReg.getName(), e);
                        result.addFailure(customerReg, "处理失败: " + e.getMessage());
                        processedCount++;
                    }
                }
                
                // 批次间短暂休息，避免过度占用资源
                if (endIndex < customerRegList.size()) {
                    Thread.sleep(50);
                }
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("任务被取消", e);
        } catch (Exception e) {
            log.error("批量处理失败: taskId={}", taskId, e);
            throw new RuntimeException("批量处理失败: " + e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 检查任务是否被取消
     */
    private void checkCancellation(String taskId) throws InterruptedException {
        if (Thread.currentThread().isInterrupted()) {
            throw new InterruptedException("任务被取消");
        }
    }

    @Override
    public boolean cancelImportTask(String taskId) {
        Thread taskThread = runningTasks.get(taskId);
        if (taskThread != null && taskThread.isAlive()) {
            taskThread.interrupt();
            runningTasks.remove(taskId);
            log.info("导入任务已取消: taskId={}", taskId);
            return true;
        }
        return false;
    }

    @Override
    public JSONObject getImportResult(String taskId) {
        try {
            ImportTask importTask = importTaskService.getById(taskId);
            if (importTask == null) {
                return null;
            }
            
            JSONObject result = new JSONObject();
            result.put("taskId", taskId);
            result.put("status", importTask.getStatus());
            result.put("totalCount", importTask.getTotalCount());
            result.put("successCount", importTask.getSuccessCount());
            result.put("failureCount", importTask.getFailureCount());
            
            // 添加详细结果数据
            if (importTask.getResultData() != null) {
                JSONObject resultData = JSON.parseObject(importTask.getResultData());
                result.putAll(resultData);
            }
            
            // 添加汇总信息
            JSONObject summary = new JSONObject();
            summary.put("fileName", importTask.getFileName());
            summary.put("fileSize", importTask.getFileSize());
            summary.put("companyName", importTask.getCompanyName());
            summary.put("startTime", importTask.getStartTime() != null ? importTask.getStartTime().getTime() : null);
            summary.put("endTime", importTask.getEndTime() != null ? importTask.getEndTime().getTime() : null);
            summary.put("duration", importTask.getDuration());
            result.put("summary", summary);
            
            return result;
        } catch (Exception e) {
            log.error("获取导入结果失败: taskId={}", taskId, e);
            return null;
        }
    }

    @Override
    public void downloadErrorReport(String taskId, HttpServletResponse response) {
        try {
            ImportTask importTask = importTaskService.getById(taskId);
            if (importTask == null) {
                throw new RuntimeException("任务不存在");
            }
            
            // 这里可以调用现有的错误报告生成逻辑
            // 暂时返回简单的错误信息
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", 
                String.format("attachment; filename=\"%s_error_report.xlsx\"", taskId));
            
            // TODO: 实现错误报告Excel生成
            log.info("错误报告下载: taskId={}", taskId);
            
        } catch (Exception e) {
            log.error("下载错误报告失败: taskId={}", taskId, e);
            throw new RuntimeException("下载错误报告失败", e);
        }
    }

    @Override
    public String retryImportTask(String taskId) {
        try {
            ImportTask originalTask = importTaskService.getById(taskId);
            if (originalTask == null || !importTaskService.canRetryTask(taskId)) {
                return null;
            }
            
            // 检查是否有保存的文件数据
            byte[] fileData = taskFileData.get(taskId);
            if (fileData == null) {
                log.error("重试任务失败，未找到原始文件数据: taskId={}", taskId);
                return null;
            }
            
            // 创建新的重试任务
            String newTaskId = importTaskService.retryFailedTask(taskId, originalTask.getCreateBy());
            if (newTaskId != null) {
                // 复制文件数据到新任务
                taskFileData.put(newTaskId, fileData);
                log.info("创建重试任务成功: originalTaskId={}, newTaskId={}", taskId, newTaskId);
            }
            
            return newTaskId;
        } catch (Exception e) {
            log.error("重试导入任务失败: taskId={}", taskId, e);
            return null;
        }
    }

    // 其他接口方法的简单实现...
    @Override
    public boolean isTaskRunning(String taskId) {
        return runningTasks.containsKey(taskId);
    }

    @Override
    public JSONObject getTaskStatus(String taskId) {
        ImportProgressInfo progressInfo = importProgressService.getProgress(taskId);
        if (progressInfo != null) {
            return (JSONObject) JSON.toJSON(progressInfo);
        }
        return null;
    }

    @Override
    public int cleanupExpiredTasks(int hours) {
        // 清理过期的文件数据
        taskFileData.clear();
        return importTaskService.cleanupExpiredTasks(hours * 24);
    }

    @Override
    public JSONObject getSystemStatistics() {
        JSONObject stats = new JSONObject();
        stats.put("runningTasks", runningTasks.size());
        stats.put("cachedFiles", taskFileData.size());
        stats.put("activeConnections", importProgressService.getActiveSessionCount());
        stats.put("activeTasks", importProgressService.getActiveTaskCount());
        return stats;
    }

    // 其他方法的占位实现...
    @Override public boolean pauseTask(String taskId) { return false; }
    @Override public boolean resumeTask(String taskId) { return false; }
    @Override public int batchCancelTasks(String[] taskIds) { return 0; }
    @Override public JSONObject getTaskLogs(String taskId, int limit) { return new JSONObject(); }
    @Override public JSONObject validateImportFile(MultipartFile file) { return new JSONObject(); }
    @Override public JSONObject previewImportData(MultipartFile file, int previewRows) { return new JSONObject(); }
    @Override public void downloadImportTemplate(HttpServletResponse response) {}
    @Override public void exportSuccessRecords(String taskId, HttpServletResponse response) {}
    @Override public JSONObject getImportConfig() { return new JSONObject(); }
    @Override public boolean updateImportConfig(JSONObject config) { return false; }
    @Override public JSONObject checkSystemResources() { return new JSONObject(); }
    @Override public boolean forceStopTask(String taskId, String reason) { return cancelImportTask(taskId); }

    /**
     * 批量检查重复记录
     * 复用现有的CustomerRegService逻辑
     */
    private Map<String, CustomerReg> batchCheckExistingRegs(List<CustomerReg> customerRegList, String companyId) {
        try {
            // 调用现有服务的批量检查方法
            // 这里需要根据实际的CustomerRegService接口调整
            return new ConcurrentHashMap<>(); // 暂时返回空Map
        } catch (Exception e) {
            log.error("批量检查重复记录失败: companyId={}", companyId, e);
            return new ConcurrentHashMap<>();
        }
    }

    /**
     * 处理单条客户登记记录
     * 复用现有的CustomerRegService逻辑
     */
    private String processCustomerRegRecordWithCache(CustomerReg customerReg, CompanyReg companyReg,
                                                   List<CompanyTeam> companyTeams, LoginUser sysUser,
                                                   Map<String, CustomerReg> existingRegsMap) {
        try {
            // 这里应该调用现有的processCustomerRegRecord方法
            // 由于该方法在CustomerRegServiceImpl中是private的，
            // 我们需要通过反射或者将其提取为public方法来调用

            // 暂时返回null表示处理成功
            // 实际实现中需要调用现有的业务逻辑
            return null;
        } catch (Exception e) {
            log.error("处理客户登记记录失败: name={}", customerReg.getName(), e);
            return "处理失败: " + e.getMessage();
        }
    }

    /**
     * 保存导入错误记录
     * 复用现有的错误记录保存逻辑
     */
    private void saveImportErrorRecords(String companyRegId, List<BatchResult.FailureResult<CustomerReg>> failureResults, LoginUser sysUser) {
        try {
            if (CollectionUtils.isEmpty(failureResults)) {
                return;
            }

            // 这里应该调用现有的CompanyImportRecordService
            // 复用现有的错误记录保存逻辑
            log.info("保存导入错误记录: companyRegId={}, 错误记录数={}", companyRegId, failureResults.size());

        } catch (Exception e) {
            log.error("保存错误记录失败: companyRegId={}", companyRegId, e);
        }
    }

    /**
     * 带回调的批量处理结果类
     */
    private static class BatchProcessResultWithCallback {
        private final List<CustomerReg> successList = new java.util.ArrayList<>();
        private final List<BatchResult.FailureResult<CustomerReg>> failureResults = new java.util.ArrayList<>();

        public void addSuccess(CustomerReg customerReg) {
            successList.add(customerReg);
        }

        public void addFailure(CustomerReg customerReg, String errorMessage) {
            failureResults.add(new BatchResult.FailureResult<>(customerReg, errorMessage));
        }

        public List<CustomerReg> getSuccessList() {
            return successList;
        }

        public List<BatchResult.FailureResult<CustomerReg>> getFailureResults() {
            return failureResults;
        }

        public int getSuccessCount() {
            return successList.size();
        }

        public int getFailureCount() {
            return failureResults.size();
        }
    }
}
