# WebSocket配置
websocket:
  # 允许的源地址（生产环境应该限制具体域名）
  allowed-origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
    - "http://127.0.0.1:3000"
    - "http://127.0.0.1:8080"
    - "${FRONTEND_URL:http://localhost:3000}"
  
  # 消息大小限制（字节）
  max-text-message-size: ${WEBSOCKET_MAX_TEXT_MESSAGE_SIZE:65536}  # 64KB
  max-binary-message-size: ${WEBSOCKET_MAX_BINARY_MESSAGE_SIZE:65536}  # 64KB
  
  # 会话超时时间（毫秒）
  max-session-idle-timeout: ${WEBSOCKET_MAX_SESSION_IDLE_TIMEOUT:300000}  # 5分钟
  
  # 心跳配置
  heartbeat:
    # 心跳间隔（秒）
    interval: ${WEBSOCKET_HEARTBEAT_INTERVAL:30}
    # 会话超时时间（秒）
    session-timeout: ${WEBSOCKET_SESSION_TIMEOUT:300}
  
  # 连接池配置
  connection:
    # 最大连接数
    max-connections: ${WEBSOCKET_MAX_CONNECTIONS:1000}
    # 连接超时时间（毫秒）
    connection-timeout: ${WEBSOCKET_CONNECTION_TIMEOUT:30000}
    # 异步发送超时时间（毫秒）
    async-send-timeout: ${WEBSOCKET_ASYNC_SEND_TIMEOUT:30000}

# 异步导入配置
async:
  import:
    # 是否启用异步导入功能
    enabled: ${ASYNC_IMPORT_ENABLED:true}
    
    # 线程池配置
    executor:
      # 核心线程数
      core-pool-size: ${IMPORT_CORE_POOL_SIZE:2}
      # 最大线程数
      max-pool-size: ${IMPORT_MAX_POOL_SIZE:5}
      # 队列容量
      queue-capacity: ${IMPORT_QUEUE_CAPACITY:10}
      # 线程空闲时间(秒)
      keep-alive-seconds: ${IMPORT_KEEP_ALIVE:60}
      # 线程名前缀
      thread-name-prefix: "Import-Async-"
    
    # 文件上传限制
    file:
      # 最大文件大小(MB)
      max-size: ${IMPORT_MAX_FILE_SIZE:50}
      # 支持的文件类型
      allowed-types: 
        - "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        - "application/vnd.ms-excel"
        - ".xlsx"
        - ".xls"
      # 临时文件存储路径
      temp-path: ${IMPORT_TEMP_PATH:/tmp/import}
    
    # 进度推送配置
    progress:
      # 进度更新间隔(毫秒)
      update-interval: ${PROGRESS_UPDATE_INTERVAL:1000}
      # 进度缓存过期时间(分钟)
      cache-expire: ${PROGRESS_CACHE_EXPIRE:30}
      # 批量更新大小
      batch-size: ${PROGRESS_BATCH_SIZE:100}
    
    # 任务管理配置
    task:
      # 任务历史保留天数
      history-days: ${TASK_HISTORY_DAYS:30}
      # 失败任务重试次数
      max-retry: ${TASK_MAX_RETRY:3}
      # 任务超时时间(分钟)
      timeout: ${TASK_TIMEOUT:60}
      # 清理任务执行间隔(小时)
      cleanup-interval: ${TASK_CLEANUP_INTERVAL:24}

# Spring Boot配置
spring:
  # WebSocket相关配置
  websocket:
    # 启用WebSocket支持
    enabled: true
  
  # 文件上传配置
  servlet:
    multipart:
      # 最大文件大小
      max-file-size: ${IMPORT_MAX_FILE_SIZE:50}MB
      # 最大请求大小
      max-request-size: ${IMPORT_MAX_REQUEST_SIZE:100}MB
      # 文件大小阈值
      file-size-threshold: 2KB
      # 临时文件位置
      location: ${IMPORT_TEMP_PATH:/tmp}

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,websocket-metrics
  endpoint:
    websocket-metrics:
      enabled: true
  metrics:
    tags:
      feature: websocket-import
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    org.jeecg.modules.reg.handler.ImportProgressWebSocketHandler: INFO
    org.jeecg.modules.reg.service.ImportProgressService: INFO
    org.jeecg.config.WebSocketConfig: INFO
    org.springframework.web.socket: DEBUG
    # WebSocket相关日志
    org.springframework.web.socket.config: DEBUG
    org.springframework.web.socket.server: DEBUG

# 安全配置
security:
  websocket:
    # 是否启用WebSocket安全检查
    enabled: ${WEBSOCKET_SECURITY_ENABLED:true}
    # 允许的最大连接数（每个IP）
    max-connections-per-ip: ${WEBSOCKET_MAX_CONNECTIONS_PER_IP:10}
    # 连接频率限制（每分钟）
    connection-rate-limit: ${WEBSOCKET_CONNECTION_RATE_LIMIT:60}

# 性能调优配置
performance:
  websocket:
    # 是否启用消息压缩
    enable-compression: ${WEBSOCKET_ENABLE_COMPRESSION:true}
    # 消息队列大小
    message-queue-size: ${WEBSOCKET_MESSAGE_QUEUE_SIZE:1000}
    # 发送缓冲区大小
    send-buffer-size: ${WEBSOCKET_SEND_BUFFER_SIZE:8192}
    # 接收缓冲区大小
    receive-buffer-size: ${WEBSOCKET_RECEIVE_BUFFER_SIZE:8192}

# 开发环境特殊配置
---
spring:
  profiles: dev
websocket:
  allowed-origins:
    - "*"  # 开发环境允许所有源
logging:
  level:
    org.springframework.web.socket: DEBUG
    org.jeecg.modules.reg.handler: DEBUG

---
spring:
  profiles: test
websocket:
  allowed-origins:
    - "http://localhost:*"
    - "http://127.0.0.1:*"
async:
  import:
    executor:
      core-pool-size: 1
      max-pool-size: 2
      queue-capacity: 5

---
spring:
  profiles: prod
websocket:
  allowed-origins:
    - "${FRONTEND_DOMAIN}"
    - "${API_DOMAIN}"
security:
  websocket:
    enabled: true
    max-connections-per-ip: 5
    connection-rate-limit: 30
logging:
  level:
    org.springframework.web.socket: WARN
    org.jeecg.modules.reg.handler: INFO
