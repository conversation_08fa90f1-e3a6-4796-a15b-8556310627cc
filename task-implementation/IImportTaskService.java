package org.jeecg.modules.reg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.ImportTask;
import org.jeecg.modules.reg.mapper.ImportTaskMapper.ImportTaskStatistics;

import java.util.List;

/**
 * @Description: 导入任务服务接口
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
public interface IImportTaskService extends IService<ImportTask> {

    /**
     * 创建导入任务
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param companyRegId 单位登记ID
     * @param companyName 单位名称
     * @param createBy 创建人
     * @return 任务ID
     */
    String createImportTask(String taskName, String taskType, String fileName, 
                           Long fileSize, String companyRegId, String companyName, String createBy);

    /**
     * 开始任务处理
     * @param taskId 任务ID
     * @param totalCount 总记录数
     * @return 是否成功
     */
    boolean startTask(String taskId, Integer totalCount);

    /**
     * 更新任务进度
     * @param taskId 任务ID
     * @param processedCount 已处理记录数
     * @param successCount 成功记录数
     * @param failureCount 失败记录数
     * @param currentMessage 当前处理信息
     * @return 是否成功
     */
    boolean updateTaskProgress(String taskId, Integer processedCount, Integer successCount, 
                              Integer failureCount, String currentMessage);

    /**
     * 完成任务
     * @param taskId 任务ID
     * @param successCount 成功记录数
     * @param failureCount 失败记录数
     * @param resultData 结果数据（JSON格式）
     * @return 是否成功
     */
    boolean completeTask(String taskId, Integer successCount, Integer failureCount, String resultData);

    /**
     * 任务失败
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 是否成功
     */
    boolean failTask(String taskId, String errorMessage);

    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelTask(String taskId);

    /**
     * 根据状态查询任务列表
     * @param status 任务状态
     * @return 任务列表
     */
    List<ImportTask> getTasksByStatus(String status);

    /**
     * 根据单位ID查询任务列表
     * @param companyRegId 单位登记ID
     * @return 任务列表
     */
    List<ImportTask> getTasksByCompanyRegId(String companyRegId);

    /**
     * 根据创建人查询任务列表
     * @param createBy 创建人
     * @return 任务列表
     */
    List<ImportTask> getTasksByCreateBy(String createBy);

    /**
     * 检查单位是否有正在处理的任务
     * @param companyRegId 单位登记ID
     * @return 是否有正在处理的任务
     */
    boolean hasProcessingTask(String companyRegId);

    /**
     * 获取正在处理的任务数量
     * @param companyRegId 单位登记ID
     * @return 正在处理的任务数量
     */
    int getProcessingTaskCount(String companyRegId);

    /**
     * 获取任务统计信息
     * @param companyRegId 单位登记ID
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 统计信息
     */
    ImportTaskStatistics getTaskStatistics(String companyRegId, String startTime, String endTime);

    /**
     * 清理过期任务
     * @param days 保留天数
     * @return 清理的任务数量
     */
    int cleanupExpiredTasks(int days);

    /**
     * 重试失败的任务
     * @param taskId 原任务ID
     * @param createBy 创建人
     * @return 新任务ID
     */
    String retryFailedTask(String taskId, String createBy);

    /**
     * 获取任务详细信息（包含结果数据）
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    ImportTask getTaskDetail(String taskId);

    /**
     * 批量更新任务状态（用于系统维护）
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param errorMessage 错误信息
     * @return 更新的任务数量
     */
    int batchUpdateTaskStatus(String oldStatus, String newStatus, String errorMessage);

    /**
     * 验证任务是否可以取消
     * @param taskId 任务ID
     * @return 是否可以取消
     */
    boolean canCancelTask(String taskId);

    /**
     * 验证任务是否可以重试
     * @param taskId 任务ID
     * @return 是否可以重试
     */
    boolean canRetryTask(String taskId);

    /**
     * 获取用户的任务历史
     * @param createBy 创建人
     * @param limit 限制数量
     * @return 任务历史列表
     */
    List<ImportTask> getUserTaskHistory(String createBy, int limit);

    /**
     * 获取单位的任务历史
     * @param companyRegId 单位登记ID
     * @param limit 限制数量
     * @return 任务历史列表
     */
    List<ImportTask> getCompanyTaskHistory(String companyRegId, int limit);
}
