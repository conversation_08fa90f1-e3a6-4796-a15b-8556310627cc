package org.jeecg.modules.reg.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.entity.ImportTask;
import org.jeecg.modules.reg.entity.ImportProgressInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Description: 任务控制服务
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class TaskControlService {

    @Autowired
    private IImportTaskService importTaskService;
    
    @Autowired
    private ImportProgressService importProgressService;
    
    @Autowired
    private AsyncImportService asyncImportService;

    // 任务控制状态映射
    private final Map<String, TaskControlState> taskControlStates = new ConcurrentHashMap<>();
    
    // 任务优先级映射
    private final Map<String, TaskPriority> taskPriorities = new ConcurrentHashMap<>();

    /**
     * 暂停任务
     * 
     * @param taskId 任务ID
     * @param reason 暂停原因
     * @return 是否成功暂停
     */
    public boolean pauseTask(String taskId, String reason) {
        try {
            log.info("暂停任务请求: taskId={}, reason={}", taskId, reason);
            
            // 检查任务是否存在且可以暂停
            ImportTask task = importTaskService.getById(taskId);
            if (task == null) {
                log.warn("任务不存在: taskId={}", taskId);
                return false;
            }
            
            if (!canPauseTask(task)) {
                log.warn("任务状态不允许暂停: taskId={}, status={}", taskId, task.getStatus());
                return false;
            }
            
            // 设置暂停标志
            TaskControlState controlState = getOrCreateControlState(taskId);
            controlState.setPaused(true);
            controlState.setPauseReason(reason);
            controlState.setPauseTime(System.currentTimeMillis());
            
            // 更新任务状态
            boolean updated = importTaskService.pauseTask(taskId, reason);
            if (updated) {
                // 更新进度信息
                importProgressService.updateProcessProgress(taskId, 
                    task.getProcessedCount() != null ? task.getProcessedCount() : 0,
                    task.getSuccessCount() != null ? task.getSuccessCount() : 0,
                    task.getFailureCount() != null ? task.getFailureCount() : 0,
                    "任务已暂停: " + reason);
                
                log.info("任务暂停成功: taskId={}", taskId);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("暂停任务失败: taskId={}", taskId, e);
            return false;
        }
    }

    /**
     * 恢复任务
     * 
     * @param taskId 任务ID
     * @return 是否成功恢复
     */
    public boolean resumeTask(String taskId) {
        try {
            log.info("恢复任务请求: taskId={}", taskId);
            
            // 检查任务是否存在且可以恢复
            ImportTask task = importTaskService.getById(taskId);
            if (task == null) {
                log.warn("任务不存在: taskId={}", taskId);
                return false;
            }
            
            if (!canResumeTask(task)) {
                log.warn("任务状态不允许恢复: taskId={}, status={}", taskId, task.getStatus());
                return false;
            }
            
            // 清除暂停标志
            TaskControlState controlState = getOrCreateControlState(taskId);
            controlState.setPaused(false);
            controlState.setPauseReason(null);
            controlState.setResumeTime(System.currentTimeMillis());
            
            // 计算暂停时长
            if (controlState.getPauseTime() != null) {
                long pauseDuration = System.currentTimeMillis() - controlState.getPauseTime();
                controlState.setTotalPauseDuration(controlState.getTotalPauseDuration() + pauseDuration);
            }
            
            // 更新任务状态
            boolean updated = importTaskService.resumeTask(taskId);
            if (updated) {
                // 更新进度信息
                importProgressService.updateProcessProgress(taskId,
                    task.getProcessedCount() != null ? task.getProcessedCount() : 0,
                    task.getSuccessCount() != null ? task.getSuccessCount() : 0,
                    task.getFailureCount() != null ? task.getFailureCount() : 0,
                    "任务已恢复");
                
                log.info("任务恢复成功: taskId={}", taskId);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("恢复任务失败: taskId={}", taskId, e);
            return false;
        }
    }

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @param reason 取消原因
     * @return 是否成功取消
     */
    public boolean cancelTask(String taskId, String reason) {
        try {
            log.info("取消任务请求: taskId={}, reason={}", taskId, reason);
            
            // 检查任务是否可以取消
            if (!importTaskService.canCancelTask(taskId)) {
                log.warn("任务状态不允许取消: taskId={}", taskId);
                return false;
            }
            
            // 取消异步执行
            boolean asyncCancelled = asyncImportService.cancelImportTask(taskId);
            
            // 更新任务状态
            boolean taskCancelled = importTaskService.cancelTask(taskId);
            
            if (taskCancelled) {
                // 更新进度信息
                importProgressService.cancelProgress(taskId, reason);
                
                // 清理控制状态
                taskControlStates.remove(taskId);
                taskPriorities.remove(taskId);
                
                log.info("任务取消成功: taskId={}, asyncCancelled={}", taskId, asyncCancelled);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("取消任务失败: taskId={}", taskId, e);
            return false;
        }
    }

    /**
     * 重试任务
     * 
     * @param taskId 原任务ID
     * @param retryReason 重试原因
     * @return 新任务ID，失败返回null
     */
    public String retryTask(String taskId, String retryReason) {
        try {
            log.info("重试任务请求: taskId={}, reason={}", taskId, retryReason);
            
            // 检查任务是否可以重试
            if (!importTaskService.canRetryTask(taskId)) {
                log.warn("任务状态不允许重试: taskId={}", taskId);
                return null;
            }
            
            // 创建重试任务
            String newTaskId = asyncImportService.retryImportTask(taskId);
            if (newTaskId != null) {
                // 复制优先级设置
                TaskPriority originalPriority = taskPriorities.get(taskId);
                if (originalPriority != null) {
                    taskPriorities.put(newTaskId, originalPriority);
                }
                
                log.info("任务重试成功: originalTaskId={}, newTaskId={}", taskId, newTaskId);
                return newTaskId;
            }
            
            return null;
        } catch (Exception e) {
            log.error("重试任务失败: taskId={}", taskId, e);
            return null;
        }
    }

    /**
     * 设置任务优先级
     * 
     * @param taskId 任务ID
     * @param priority 优先级
     * @return 是否设置成功
     */
    public boolean setTaskPriority(String taskId, TaskPriority priority) {
        try {
            log.info("设置任务优先级: taskId={}, priority={}", taskId, priority);
            
            // 验证任务存在
            ImportTask task = importTaskService.getById(taskId);
            if (task == null) {
                log.warn("任务不存在: taskId={}", taskId);
                return false;
            }
            
            // 设置优先级
            taskPriorities.put(taskId, priority);
            
            // 更新任务记录（如果需要持久化优先级）
            JSONObject extraInfo = new JSONObject();
            extraInfo.put("priority", priority.name());
            extraInfo.put("prioritySetTime", System.currentTimeMillis());
            
            boolean updated = importTaskService.updateTaskExtraInfo(taskId, extraInfo.toJSONString());
            
            log.info("任务优先级设置{}: taskId={}, priority={}", 
                    updated ? "成功" : "失败", taskId, priority);
            return updated;
        } catch (Exception e) {
            log.error("设置任务优先级失败: taskId={}", taskId, e);
            return false;
        }
    }

    /**
     * 批量取消任务
     * 
     * @param taskIds 任务ID列表
     * @param reason 取消原因
     * @return 成功取消的任务数量
     */
    public int batchCancelTasks(List<String> taskIds, String reason) {
        int successCount = 0;
        
        for (String taskId : taskIds) {
            try {
                if (cancelTask(taskId, reason)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量取消任务失败: taskId={}", taskId, e);
            }
        }
        
        log.info("批量取消任务完成: 总数={}, 成功={}", taskIds.size(), successCount);
        return successCount;
    }

    /**
     * 强制停止任务
     * 
     * @param taskId 任务ID
     * @param reason 停止原因
     * @return 是否成功停止
     */
    public boolean forceStopTask(String taskId, String reason) {
        try {
            log.warn("强制停止任务: taskId={}, reason={}", taskId, reason);
            
            // 强制停止异步执行
            boolean asyncStopped = asyncImportService.forceStopTask(taskId, reason);
            
            // 强制更新任务状态为失败
            boolean taskStopped = importTaskService.failTask(taskId, "强制停止: " + reason);
            
            if (taskStopped) {
                // 更新进度信息
                importProgressService.failProgress(taskId, "任务被强制停止: " + reason);
                
                // 清理控制状态
                taskControlStates.remove(taskId);
                taskPriorities.remove(taskId);
                
                log.warn("任务强制停止成功: taskId={}, asyncStopped={}", taskId, asyncStopped);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("强制停止任务失败: taskId={}", taskId, e);
            return false;
        }
    }

    /**
     * 获取任务控制状态
     * 
     * @param taskId 任务ID
     * @return 控制状态信息
     */
    public JSONObject getTaskControlStatus(String taskId) {
        try {
            JSONObject status = new JSONObject();
            
            // 基本任务信息
            ImportTask task = importTaskService.getById(taskId);
            if (task != null) {
                status.put("taskId", taskId);
                status.put("status", task.getStatus());
                status.put("canPause", canPauseTask(task));
                status.put("canResume", canResumeTask(task));
                status.put("canCancel", importTaskService.canCancelTask(taskId));
                status.put("canRetry", importTaskService.canRetryTask(taskId));
            }
            
            // 控制状态信息
            TaskControlState controlState = taskControlStates.get(taskId);
            if (controlState != null) {
                status.put("isPaused", controlState.isPaused());
                status.put("pauseReason", controlState.getPauseReason());
                status.put("pauseTime", controlState.getPauseTime());
                status.put("resumeTime", controlState.getResumeTime());
                status.put("totalPauseDuration", controlState.getTotalPauseDuration());
            }
            
            // 优先级信息
            TaskPriority priority = taskPriorities.get(taskId);
            if (priority != null) {
                status.put("priority", priority.name());
                status.put("priorityLevel", priority.getLevel());
            }
            
            return status;
        } catch (Exception e) {
            log.error("获取任务控制状态失败: taskId={}", taskId, e);
            return new JSONObject();
        }
    }

    /**
     * 检查任务是否可以暂停
     */
    private boolean canPauseTask(ImportTask task) {
        return ImportTask.TaskStatus.PROCESSING.getCode().equals(task.getStatus());
    }

    /**
     * 检查任务是否可以恢复
     */
    private boolean canResumeTask(ImportTask task) {
        return ImportTask.TaskStatus.PAUSED.getCode().equals(task.getStatus());
    }

    /**
     * 获取或创建任务控制状态
     */
    private TaskControlState getOrCreateControlState(String taskId) {
        return taskControlStates.computeIfAbsent(taskId, k -> new TaskControlState());
    }

    /**
     * 清理过期的控制状态
     */
    public void cleanupExpiredControlStates() {
        try {
            long expireTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000; // 24小时
            
            taskControlStates.entrySet().removeIf(entry -> {
                TaskControlState state = entry.getValue();
                return state.getCreateTime() < expireTime;
            });
            
            taskPriorities.entrySet().removeIf(entry -> {
                String taskId = entry.getKey();
                ImportTask task = importTaskService.getById(taskId);
                return task == null || task.isCompleted() || task.isFailed() || task.isCancelled();
            });
            
            log.debug("清理过期控制状态完成");
        } catch (Exception e) {
            log.error("清理过期控制状态失败", e);
        }
    }

    /**
     * 任务控制状态类
     */
    public static class TaskControlState {
        private final AtomicBoolean paused = new AtomicBoolean(false);
        private volatile String pauseReason;
        private volatile Long pauseTime;
        private volatile Long resumeTime;
        private volatile long totalPauseDuration = 0;
        private final long createTime = System.currentTimeMillis();

        public boolean isPaused() { return paused.get(); }
        public void setPaused(boolean paused) { this.paused.set(paused); }
        
        public String getPauseReason() { return pauseReason; }
        public void setPauseReason(String pauseReason) { this.pauseReason = pauseReason; }
        
        public Long getPauseTime() { return pauseTime; }
        public void setPauseTime(Long pauseTime) { this.pauseTime = pauseTime; }
        
        public Long getResumeTime() { return resumeTime; }
        public void setResumeTime(Long resumeTime) { this.resumeTime = resumeTime; }
        
        public long getTotalPauseDuration() { return totalPauseDuration; }
        public void setTotalPauseDuration(long totalPauseDuration) { this.totalPauseDuration = totalPauseDuration; }
        
        public long getCreateTime() { return createTime; }
    }

    /**
     * 任务优先级枚举
     */
    public enum TaskPriority {
        LOW(1, "低优先级"),
        NORMAL(5, "普通优先级"),
        HIGH(8, "高优先级"),
        URGENT(10, "紧急优先级");

        private final int level;
        private final String description;

        TaskPriority(int level, String description) {
            this.level = level;
            this.description = description;
        }

        public int getLevel() { return level; }
        public String getDescription() { return description; }
    }
}
