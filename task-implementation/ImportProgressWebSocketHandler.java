package org.jeecg.modules.reg.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.entity.ImportProgressInfo;
import org.jeecg.modules.reg.service.ImportProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 导入进度WebSocket处理器
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Slf4j
@Component
public class ImportProgressWebSocketHandler implements WebSocketHandler {

    @Autowired
    private ImportProgressService importProgressService;

    // 存储会话的最后活跃时间
    private final Map<String, Long> sessionLastActiveTime = new ConcurrentHashMap<>();
    
    // 心跳检测调度器
    private final ScheduledExecutorService heartbeatScheduler = Executors.newScheduledThreadPool(1);
    
    // 心跳间隔（秒）
    private static final int HEARTBEAT_INTERVAL = 30;
    
    // 会话超时时间（秒）
    private static final int SESSION_TIMEOUT = 300;

    /**
     * 构造函数，启动心跳检测
     */
    public ImportProgressWebSocketHandler() {
        startHeartbeatCheck();
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        try {
            // 从URL参数中获取taskId
            String taskId = getTaskIdFromSession(session);
            if (taskId != null) {
                // 添加会话到进度服务
                importProgressService.addSession(taskId, session);
                
                // 记录会话活跃时间
                sessionLastActiveTime.put(session.getId(), System.currentTimeMillis());
                
                log.info("WebSocket连接建立成功: taskId={}, sessionId={}, remoteAddress={}", 
                        taskId, session.getId(), session.getRemoteAddress());
                
                // 发送连接成功消息
                sendMessage(session, createConnectionMessage("connected", "连接建立成功"));
                
                // 发送当前进度信息
                ImportProgressInfo progressInfo = importProgressService.getProgress(taskId);
                if (progressInfo != null) {
                    sendMessage(session, JSON.toJSONString(progressInfo.createSimplified()));
                    log.debug("发送当前进度信息: taskId={}, progress={}%", taskId, progressInfo.getProgress());
                }
            } else {
                log.warn("WebSocket连接缺少taskId参数: sessionId={}, uri={}", 
                        session.getId(), session.getUri());
                sendMessage(session, createErrorMessage("missing_task_id", "缺少taskId参数"));
                session.close(CloseStatus.BAD_DATA);
            }
        } catch (Exception e) {
            log.error("WebSocket连接建立失败: sessionId={}", session.getId(), e);
            try {
                sendMessage(session, createErrorMessage("connection_error", "连接建立失败: " + e.getMessage()));
                session.close(CloseStatus.SERVER_ERROR);
            } catch (Exception ex) {
                log.error("发送错误消息失败", ex);
            }
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        try {
            // 更新会话活跃时间
            sessionLastActiveTime.put(session.getId(), System.currentTimeMillis());
            
            String payload = message.getPayload().toString();
            log.debug("收到WebSocket消息: sessionId={}, message={}", session.getId(), payload);
            
            // 解析消息
            JSONObject messageObj = JSON.parseObject(payload);
            String type = messageObj.getString("type");
            
            switch (type) {
                case "ping":
                    // 心跳消息
                    sendMessage(session, createPongMessage());
                    break;
                case "get_progress":
                    // 获取进度请求
                    handleGetProgressRequest(session, messageObj);
                    break;
                case "cancel_task":
                    // 取消任务请求
                    handleCancelTaskRequest(session, messageObj);
                    break;
                default:
                    log.warn("未知的消息类型: type={}, sessionId={}", type, session.getId());
                    sendMessage(session, createErrorMessage("unknown_message_type", "未知的消息类型: " + type));
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败: sessionId={}", session.getId(), e);
            sendMessage(session, createErrorMessage("message_processing_error", "消息处理失败: " + e.getMessage()));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String taskId = getTaskIdFromSession(session);
        log.error("WebSocket传输错误: taskId={}, sessionId={}, error={}", 
                taskId, session.getId(), exception.getMessage(), exception);
        
        // 清理会话
        cleanupSession(session, taskId);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String taskId = getTaskIdFromSession(session);
        log.info("WebSocket连接关闭: taskId={}, sessionId={}, status={}, reason={}", 
                taskId, session.getId(), closeStatus.getCode(), closeStatus.getReason());
        
        // 清理会话
        cleanupSession(session, taskId);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 从WebSocket会话中获取taskId
     */
    private String getTaskIdFromSession(WebSocketSession session) {
        try {
            URI uri = session.getUri();
            if (uri != null) {
                String query = uri.getQuery();
                if (query != null) {
                    String[] params = query.split("&");
                    for (String param : params) {
                        String[] keyValue = param.split("=");
                        if (keyValue.length == 2 && "taskId".equals(keyValue[0])) {
                            return keyValue[1];
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析WebSocket URL参数失败: sessionId={}", session.getId(), e);
        }
        return null;
    }

    /**
     * 发送消息到客户端
     */
    private void sendMessage(WebSocketSession session, String message) {
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(message));
            }
        } catch (Exception e) {
            log.error("发送WebSocket消息失败: sessionId={}", session.getId(), e);
        }
    }

    /**
     * 处理获取进度请求
     */
    private void handleGetProgressRequest(WebSocketSession session, JSONObject messageObj) {
        try {
            String taskId = messageObj.getString("taskId");
            if (taskId != null) {
                ImportProgressInfo progressInfo = importProgressService.getProgress(taskId);
                if (progressInfo != null) {
                    sendMessage(session, JSON.toJSONString(progressInfo.createSimplified()));
                } else {
                    sendMessage(session, createErrorMessage("progress_not_found", "未找到进度信息"));
                }
            } else {
                sendMessage(session, createErrorMessage("missing_task_id", "缺少taskId参数"));
            }
        } catch (Exception e) {
            log.error("处理获取进度请求失败", e);
            sendMessage(session, createErrorMessage("get_progress_error", "获取进度失败: " + e.getMessage()));
        }
    }

    /**
     * 处理取消任务请求
     */
    private void handleCancelTaskRequest(WebSocketSession session, JSONObject messageObj) {
        try {
            String taskId = messageObj.getString("taskId");
            if (taskId != null) {
                // 这里可以添加取消任务的逻辑
                // 目前只是发送确认消息
                JSONObject response = new JSONObject();
                response.put("type", "cancel_response");
                response.put("taskId", taskId);
                response.put("message", "取消请求已收到");
                sendMessage(session, response.toJSONString());
            } else {
                sendMessage(session, createErrorMessage("missing_task_id", "缺少taskId参数"));
            }
        } catch (Exception e) {
            log.error("处理取消任务请求失败", e);
            sendMessage(session, createErrorMessage("cancel_task_error", "取消任务失败: " + e.getMessage()));
        }
    }

    /**
     * 清理会话
     */
    private void cleanupSession(WebSocketSession session, String taskId) {
        try {
            if (taskId != null) {
                importProgressService.removeSession(taskId);
            }
            sessionLastActiveTime.remove(session.getId());
        } catch (Exception e) {
            log.error("清理会话失败: sessionId={}", session.getId(), e);
        }
    }

    /**
     * 创建连接消息
     */
    private String createConnectionMessage(String status, String message) {
        JSONObject response = new JSONObject();
        response.put("type", "connection");
        response.put("status", status);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return response.toJSONString();
    }

    /**
     * 创建错误消息
     */
    private String createErrorMessage(String errorCode, String errorMessage) {
        JSONObject response = new JSONObject();
        response.put("type", "error");
        response.put("errorCode", errorCode);
        response.put("errorMessage", errorMessage);
        response.put("timestamp", System.currentTimeMillis());
        return response.toJSONString();
    }

    /**
     * 创建Pong消息
     */
    private String createPongMessage() {
        JSONObject response = new JSONObject();
        response.put("type", "pong");
        response.put("timestamp", System.currentTimeMillis());
        return response.toJSONString();
    }

    /**
     * 启动心跳检测
     */
    private void startHeartbeatCheck() {
        heartbeatScheduler.scheduleWithFixedDelay(() -> {
            try {
                long currentTime = System.currentTimeMillis();
                sessionLastActiveTime.entrySet().removeIf(entry -> {
                    long lastActiveTime = entry.getValue();
                    boolean isTimeout = (currentTime - lastActiveTime) > (SESSION_TIMEOUT * 1000);
                    if (isTimeout) {
                        log.info("会话超时，自动清理: sessionId={}", entry.getKey());
                    }
                    return isTimeout;
                });
            } catch (Exception e) {
                log.error("心跳检测异常", e);
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
        
        log.info("WebSocket心跳检测已启动: 间隔={}秒, 超时={}秒", HEARTBEAT_INTERVAL, SESSION_TIMEOUT);
    }

    /**
     * 获取活跃会话数
     */
    public int getActiveSessionCount() {
        return sessionLastActiveTime.size();
    }

    /**
     * 关闭处理器
     */
    public void shutdown() {
        try {
            heartbeatScheduler.shutdown();
            if (!heartbeatScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                heartbeatScheduler.shutdownNow();
            }
            log.info("WebSocket处理器已关闭");
        } catch (InterruptedException e) {
            heartbeatScheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
