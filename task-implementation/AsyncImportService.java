package org.jeecg.modules.reg.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 异步导入服务接口
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
public interface AsyncImportService {

    /**
     * 异步处理导入任务
     * 
     * @param taskId 任务ID
     * @param file 上传的Excel文件
     * @param companyRegId 单位登记ID
     * @param sysUser 当前用户
     */
    void processImportAsync(String taskId, MultipartFile file, String companyRegId, LoginUser sysUser);

    /**
     * 取消导入任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancelImportTask(String taskId);

    /**
     * 获取导入结果详情
     * 
     * @param taskId 任务ID
     * @return 导入结果详情
     */
    JSONObject getImportResult(String taskId);

    /**
     * 下载错误报告
     * 
     * @param taskId 任务ID
     * @param response HTTP响应对象
     */
    void downloadErrorReport(String taskId, HttpServletResponse response);

    /**
     * 重试失败的导入任务
     * 
     * @param taskId 原任务ID
     * @return 新任务ID，失败返回null
     */
    String retryImportTask(String taskId);

    /**
     * 检查任务是否正在运行
     * 
     * @param taskId 任务ID
     * @return 是否正在运行
     */
    boolean isTaskRunning(String taskId);

    /**
     * 获取任务执行状态
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    JSONObject getTaskStatus(String taskId);

    /**
     * 清理过期的任务资源
     * 
     * @param hours 过期时间（小时）
     * @return 清理的任务数量
     */
    int cleanupExpiredTasks(int hours);

    /**
     * 获取系统运行统计信息
     * 
     * @return 统计信息
     */
    JSONObject getSystemStatistics();

    /**
     * 暂停任务处理（用于系统维护）
     * 
     * @param taskId 任务ID
     * @return 是否成功暂停
     */
    boolean pauseTask(String taskId);

    /**
     * 恢复任务处理
     * 
     * @param taskId 任务ID
     * @return 是否成功恢复
     */
    boolean resumeTask(String taskId);

    /**
     * 批量取消任务
     * 
     * @param taskIds 任务ID列表
     * @return 成功取消的任务数量
     */
    int batchCancelTasks(String[] taskIds);

    /**
     * 获取任务执行日志
     * 
     * @param taskId 任务ID
     * @param limit 日志条数限制
     * @return 日志列表
     */
    JSONObject getTaskLogs(String taskId, int limit);

    /**
     * 验证导入文件格式
     * 
     * @param file 上传文件
     * @return 验证结果
     */
    JSONObject validateImportFile(MultipartFile file);

    /**
     * 预览导入数据
     * 
     * @param file 上传文件
     * @param previewRows 预览行数
     * @return 预览数据
     */
    JSONObject previewImportData(MultipartFile file, int previewRows);

    /**
     * 获取导入模板
     * 
     * @param response HTTP响应对象
     */
    void downloadImportTemplate(HttpServletResponse response);

    /**
     * 导出成功记录
     * 
     * @param taskId 任务ID
     * @param response HTTP响应对象
     */
    void exportSuccessRecords(String taskId, HttpServletResponse response);

    /**
     * 获取导入配置信息
     * 
     * @return 配置信息
     */
    JSONObject getImportConfig();

    /**
     * 更新导入配置
     * 
     * @param config 配置信息
     * @return 是否成功更新
     */
    boolean updateImportConfig(JSONObject config);

    /**
     * 检查系统资源使用情况
     * 
     * @return 资源使用情况
     */
    JSONObject checkSystemResources();

    /**
     * 强制停止任务（紧急情况使用）
     * 
     * @param taskId 任务ID
     * @param reason 停止原因
     * @return 是否成功停止
     */
    boolean forceStopTask(String taskId, String reason);
}
