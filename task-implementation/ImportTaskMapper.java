package org.jeecg.modules.reg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.reg.entity.ImportTask;

import java.util.List;

/**
 * @Description: 导入任务
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Mapper
public interface ImportTaskMapper extends BaseMapper<ImportTask> {

    /**
     * 根据状态查询任务列表
     * @param status 任务状态
     * @return 任务列表
     */
    @Select("SELECT * FROM import_task WHERE status = #{status} ORDER BY create_time DESC")
    List<ImportTask> selectByStatus(@Param("status") String status);

    /**
     * 根据单位ID查询任务列表
     * @param companyRegId 单位登记ID
     * @return 任务列表
     */
    @Select("SELECT * FROM import_task WHERE company_reg_id = #{companyRegId} ORDER BY create_time DESC")
    List<ImportTask> selectByCompanyRegId(@Param("companyRegId") String companyRegId);

    /**
     * 根据创建人查询任务列表
     * @param createBy 创建人
     * @return 任务列表
     */
    @Select("SELECT * FROM import_task WHERE create_by = #{createBy} ORDER BY create_time DESC")
    List<ImportTask> selectByCreateBy(@Param("createBy") String createBy);

    /**
     * 查询正在处理的任务数量
     * @param companyRegId 单位登记ID
     * @return 正在处理的任务数量
     */
    @Select("SELECT COUNT(*) FROM import_task WHERE company_reg_id = #{companyRegId} AND status = 'PROCESSING'")
    int countProcessingTasks(@Param("companyRegId") String companyRegId);

    /**
     * 更新任务进度
     * @param id 任务ID
     * @param progress 进度百分比
     * @param processedCount 已处理记录数
     * @param successCount 成功记录数
     * @param failureCount 失败记录数
     * @param currentMessage 当前处理信息
     * @return 影响行数
     */
    @Update("UPDATE import_task SET " +
            "progress = #{progress}, " +
            "processed_count = #{processedCount}, " +
            "success_count = #{successCount}, " +
            "failure_count = #{failureCount}, " +
            "current_message = #{currentMessage}, " +
            "update_time = NOW() " +
            "WHERE id = #{id}")
    int updateProgress(@Param("id") String id,
                      @Param("progress") Integer progress,
                      @Param("processedCount") Integer processedCount,
                      @Param("successCount") Integer successCount,
                      @Param("failureCount") Integer failureCount,
                      @Param("currentMessage") String currentMessage);

    /**
     * 更新任务状态
     * @param id 任务ID
     * @param status 任务状态
     * @param errorMessage 错误信息（可选）
     * @return 影响行数
     */
    @Update("<script>" +
            "UPDATE import_task SET " +
            "status = #{status}, " +
            "update_time = NOW() " +
            "<if test='errorMessage != null'>" +
            ", error_message = #{errorMessage} " +
            "</if>" +
            "<if test='status == \"COMPLETED\" or status == \"FAILED\" or status == \"CANCELLED\"'>" +
            ", end_time = NOW(), " +
            "duration = TIMESTAMPDIFF(MICROSECOND, start_time, NOW()) / 1000 " +
            "</if>" +
            "WHERE id = #{id}" +
            "</script>")
    int updateStatus(@Param("id") String id,
                    @Param("status") String status,
                    @Param("errorMessage") String errorMessage);

    /**
     * 批量删除过期任务
     * @param days 保留天数
     * @return 删除的任务数量
     */
    @Update("DELETE FROM import_task WHERE create_time < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    int deleteExpiredTasks(@Param("days") int days);

    /**
     * 查询任务统计信息
     * @param companyRegId 单位登记ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalTasks, " +
            "SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completedTasks, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failedTasks, " +
            "SUM(CASE WHEN status = 'PROCESSING' THEN 1 ELSE 0 END) as processingTasks, " +
            "SUM(total_count) as totalRecords, " +
            "SUM(success_count) as successRecords, " +
            "SUM(failure_count) as failureRecords " +
            "FROM import_task " +
            "WHERE company_reg_id = #{companyRegId} " +
            "AND create_time BETWEEN #{startTime} AND #{endTime}")
    ImportTaskStatistics getTaskStatistics(@Param("companyRegId") String companyRegId,
                                          @Param("startTime") String startTime,
                                          @Param("endTime") String endTime);

    /**
     * 任务统计信息内部类
     */
    class ImportTaskStatistics {
        private Integer totalTasks;
        private Integer completedTasks;
        private Integer failedTasks;
        private Integer processingTasks;
        private Integer totalRecords;
        private Integer successRecords;
        private Integer failureRecords;

        // Getters and Setters
        public Integer getTotalTasks() { return totalTasks; }
        public void setTotalTasks(Integer totalTasks) { this.totalTasks = totalTasks; }

        public Integer getCompletedTasks() { return completedTasks; }
        public void setCompletedTasks(Integer completedTasks) { this.completedTasks = completedTasks; }

        public Integer getFailedTasks() { return failedTasks; }
        public void setFailedTasks(Integer failedTasks) { this.failedTasks = failedTasks; }

        public Integer getProcessingTasks() { return processingTasks; }
        public void setProcessingTasks(Integer processingTasks) { this.processingTasks = processingTasks; }

        public Integer getTotalRecords() { return totalRecords; }
        public void setTotalRecords(Integer totalRecords) { this.totalRecords = totalRecords; }

        public Integer getSuccessRecords() { return successRecords; }
        public void setSuccessRecords(Integer successRecords) { this.successRecords = successRecords; }

        public Integer getFailureRecords() { return failureRecords; }
        public void setFailureRecords(Integer failureRecords) { this.failureRecords = failureRecords; }
    }
}
