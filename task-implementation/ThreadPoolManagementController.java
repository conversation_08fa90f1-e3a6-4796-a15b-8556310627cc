package org.jeecg.modules.reg.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.config.EnhancedAsyncConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 线程池管理控制器
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Api(tags = "线程池管理")
@RestController
@RequestMapping("/system/thread-pool")
@Slf4j
public class ThreadPoolManagementController {

    @Autowired
    private EnhancedAsyncConfig.ImportExecutorMonitor importExecutorMonitor;

    @Autowired
    private EnhancedAsyncConfig.ImportExecutorHealthIndicator importExecutorHealthIndicator;

    @Autowired
    private EnhancedAsyncConfig.ImportExecutorMetrics importExecutorMetrics;

    /**
     * 获取线程池状态
     */
    @GetMapping("/status")
    @ApiOperation(value = "获取线程池状态", notes = "获取导入线程池的详细状态信息")
    public Result<EnhancedAsyncConfig.ThreadPoolStatus> getThreadPoolStatus() {
        try {
            EnhancedAsyncConfig.ThreadPoolStatus status = importExecutorMonitor.getThreadPoolStatus();
            return Result.OK(status);
        } catch (Exception e) {
            log.error("获取线程池状态失败", e);
            return Result.error("获取线程池状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取线程池健康状态
     */
    @GetMapping("/health")
    @ApiOperation(value = "获取线程池健康状态", notes = "检查导入线程池的健康状况")
    public Result<JSONObject> getThreadPoolHealth() {
        try {
            Health health = importExecutorHealthIndicator.health();
            
            JSONObject result = new JSONObject();
            result.put("status", health.getStatus().getCode());
            result.put("details", health.getDetails());
            
            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取线程池健康状态失败", e);
            return Result.error("获取健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取线程池性能指标
     */
    @GetMapping("/metrics")
    @ApiOperation(value = "获取线程池性能指标", notes = "获取导入线程池的性能统计信息")
    public Result<EnhancedAsyncConfig.PerformanceMetrics> getThreadPoolMetrics() {
        try {
            EnhancedAsyncConfig.PerformanceMetrics metrics = importExecutorMetrics.getPerformanceMetrics();
            return Result.OK(metrics);
        } catch (Exception e) {
            log.error("获取线程池性能指标失败", e);
            return Result.error("获取性能指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取线程池使用率
     */
    @GetMapping("/utilization")
    @ApiOperation(value = "获取线程池使用率", notes = "获取线程池和队列的使用率")
    public Result<JSONObject> getThreadPoolUtilization() {
        try {
            JSONObject utilization = new JSONObject();
            utilization.put("poolUtilization", importExecutorMonitor.getPoolUtilization());
            utilization.put("queueUtilization", importExecutorMonitor.getQueueUtilization());
            utilization.put("timestamp", System.currentTimeMillis());
            
            return Result.OK(utilization);
        } catch (Exception e) {
            log.error("获取线程池使用率失败", e);
            return Result.error("获取使用率失败: " + e.getMessage());
        }
    }

    /**
     * 动态调整核心线程数
     */
    @PostMapping("/adjust-core-pool-size")
    @ApiOperation(value = "调整核心线程数", notes = "动态调整导入线程池的核心线程数")
    public Result<String> adjustCorePoolSize(
            @ApiParam(value = "新的核心线程数", required = true) @RequestParam("corePoolSize") int corePoolSize) {
        try {
            if (corePoolSize <= 0) {
                return Result.error("核心线程数必须大于0");
            }
            
            boolean success = importExecutorMonitor.adjustCorePoolSize(corePoolSize);
            if (success) {
                log.info("核心线程数调整成功: {}", corePoolSize);
                return Result.OK("核心线程数调整成功");
            } else {
                return Result.error("核心线程数调整失败，请检查参数");
            }
        } catch (Exception e) {
            log.error("调整核心线程数失败", e);
            return Result.error("调整失败: " + e.getMessage());
        }
    }

    /**
     * 动态调整最大线程数
     */
    @PostMapping("/adjust-max-pool-size")
    @ApiOperation(value = "调整最大线程数", notes = "动态调整导入线程池的最大线程数")
    public Result<String> adjustMaxPoolSize(
            @ApiParam(value = "新的最大线程数", required = true) @RequestParam("maxPoolSize") int maxPoolSize) {
        try {
            if (maxPoolSize <= 0) {
                return Result.error("最大线程数必须大于0");
            }
            
            boolean success = importExecutorMonitor.adjustMaxPoolSize(maxPoolSize);
            if (success) {
                log.info("最大线程数调整成功: {}", maxPoolSize);
                return Result.OK("最大线程数调整成功");
            } else {
                return Result.error("最大线程数调整失败，请检查参数");
            }
        } catch (Exception e) {
            log.error("调整最大线程数失败", e);
            return Result.error("调整失败: " + e.getMessage());
        }
    }

    /**
     * 获取线程池配置建议
     */
    @GetMapping("/recommendations")
    @ApiOperation(value = "获取配置建议", notes = "基于当前系统负载获取线程池配置建议")
    public Result<JSONObject> getConfigurationRecommendations() {
        try {
            JSONObject recommendations = new JSONObject();
            
            // 获取当前状态
            EnhancedAsyncConfig.ThreadPoolStatus status = importExecutorMonitor.getThreadPoolStatus();
            double poolUtilization = importExecutorMonitor.getPoolUtilization();
            double queueUtilization = importExecutorMonitor.getQueueUtilization();
            
            // 基于使用率给出建议
            if (poolUtilization > 0.8) {
                recommendations.put("poolSizeRecommendation", "建议增加最大线程数，当前使用率过高");
                recommendations.put("suggestedMaxPoolSize", Math.min(status.getMaximumPoolSize() + 2, 10));
            } else if (poolUtilization < 0.3) {
                recommendations.put("poolSizeRecommendation", "可以考虑减少最大线程数，当前使用率较低");
                recommendations.put("suggestedMaxPoolSize", Math.max(status.getMaximumPoolSize() - 1, status.getCorePoolSize()));
            } else {
                recommendations.put("poolSizeRecommendation", "当前线程池大小配置合理");
            }
            
            if (queueUtilization > 0.7) {
                recommendations.put("queueRecommendation", "建议增加队列容量或增加线程数");
            } else {
                recommendations.put("queueRecommendation", "队列使用率正常");
            }
            
            // 系统资源建议
            Runtime runtime = Runtime.getRuntime();
            int availableProcessors = runtime.availableProcessors();
            long maxMemory = runtime.maxMemory();
            long freeMemory = runtime.freeMemory();
            
            recommendations.put("systemInfo", new JSONObject() {{
                put("availableProcessors", availableProcessors);
                put("maxMemoryMB", maxMemory / 1024 / 1024);
                put("freeMemoryMB", freeMemory / 1024 / 1024);
                put("memoryUsagePercent", (double)(maxMemory - freeMemory) / maxMemory * 100);
            }});
            
            if (availableProcessors > status.getMaximumPoolSize() * 2) {
                recommendations.put("cpuRecommendation", "CPU核心数充足，可以考虑增加线程数");
            } else {
                recommendations.put("cpuRecommendation", "当前线程数与CPU核心数比例合理");
            }
            
            return Result.OK(recommendations);
        } catch (Exception e) {
            log.error("获取配置建议失败", e);
            return Result.error("获取建议失败: " + e.getMessage());
        }
    }

    /**
     * 获取线程池历史统计
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取历史统计", notes = "获取线程池的历史统计信息")
    public Result<JSONObject> getThreadPoolStatistics() {
        try {
            JSONObject statistics = new JSONObject();
            
            EnhancedAsyncConfig.ThreadPoolStatus status = importExecutorMonitor.getThreadPoolStatus();
            EnhancedAsyncConfig.PerformanceMetrics metrics = importExecutorMetrics.getPerformanceMetrics();
            
            statistics.put("totalTasksExecuted", status.getCompletedTaskCount());
            statistics.put("currentActiveTasks", status.getActiveCount());
            statistics.put("peakPoolSize", status.getLargestPoolSize());
            statistics.put("averageThroughput", metrics.getThroughput());
            statistics.put("averageExecutionTime", metrics.getAverageExecutionTime());
            
            // 计算效率指标
            if (status.getTaskCount() > 0) {
                double completionRate = (double) status.getCompletedTaskCount() / status.getTaskCount() * 100;
                statistics.put("taskCompletionRate", completionRate);
            }
            
            // 添加时间戳
            statistics.put("timestamp", System.currentTimeMillis());
            statistics.put("uptime", java.lang.management.ManagementFactory.getRuntimeMXBean().getUptime());
            
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取线程池统计失败", e);
            return Result.error("获取统计失败: " + e.getMessage());
        }
    }

    /**
     * 重置线程池统计
     */
    @PostMapping("/reset-statistics")
    @ApiOperation(value = "重置统计信息", notes = "重置线程池的统计计数器（仅限管理员）")
    public Result<String> resetStatistics() {
        try {
            // 这里可以添加权限检查
            // 注意：ThreadPoolExecutor本身不支持重置统计，这里只是示例
            log.info("线程池统计重置请求（注意：实际统计由JVM管理，无法重置）");
            return Result.OK("统计重置请求已记录");
        } catch (Exception e) {
            log.error("重置统计失败", e);
            return Result.error("重置失败: " + e.getMessage());
        }
    }

    /**
     * 获取线程详细信息
     */
    @GetMapping("/threads")
    @ApiOperation(value = "获取线程详情", notes = "获取当前活跃线程的详细信息")
    public Result<JSONObject> getThreadDetails() {
        try {
            JSONObject threadInfo = new JSONObject();
            
            // 获取所有线程信息
            ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
            while (rootGroup.getParent() != null) {
                rootGroup = rootGroup.getParent();
            }
            
            Thread[] threads = new Thread[rootGroup.activeCount()];
            int count = rootGroup.enumerate(threads);
            
            int importThreadCount = 0;
            for (int i = 0; i < count; i++) {
                if (threads[i] != null && threads[i].getName().startsWith("Import-Async-")) {
                    importThreadCount++;
                }
            }
            
            threadInfo.put("totalThreads", count);
            threadInfo.put("importThreads", importThreadCount);
            threadInfo.put("timestamp", System.currentTimeMillis());
            
            return Result.OK(threadInfo);
        } catch (Exception e) {
            log.error("获取线程详情失败", e);
            return Result.error("获取线程详情失败: " + e.getMessage());
        }
    }
}
