package org.jeecg.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description: 异步任务配置
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Slf4j
@Configuration
@EnableAsync
@ConditionalOnProperty(name = "async.import.enabled", havingValue = "true", matchIfMissing = true)
public class AsyncConfig {

    @Value("${async.import.executor.core-pool-size:2}")
    private int corePoolSize;

    @Value("${async.import.executor.max-pool-size:5}")
    private int maxPoolSize;

    @Value("${async.import.executor.queue-capacity:10}")
    private int queueCapacity;

    @Value("${async.import.executor.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    @Value("${async.import.executor.thread-name-prefix:Import-Async-}")
    private String threadNamePrefix;

    /**
     * 导入任务专用线程池
     */
    @Bean("importExecutor")
    public Executor importExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(corePoolSize);
        
        // 最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        
        // 队列容量
        executor.setQueueCapacity(queueCapacity);
        
        // 线程空闲时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        
        // 线程名前缀
        executor.setThreadNamePrefix(threadNamePrefix);
        
        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 初始化
        executor.initialize();
        
        log.info("导入任务线程池配置完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}, keepAliveSeconds={}", 
                corePoolSize, maxPoolSize, queueCapacity, keepAliveSeconds);
        
        return executor;
    }

    /**
     * 通用异步任务线程池（如果需要）
     */
    @Bean("taskExecutor")
    @ConditionalOnProperty(name = "async.general.enabled", havingValue = "true", matchIfMissing = false)
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 通用任务的配置（相对保守）
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(3);
        executor.setQueueCapacity(20);
        executor.setKeepAliveSeconds(300);
        executor.setThreadNamePrefix("General-Async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("通用异步任务线程池配置完成");
        
        return executor;
    }

    /**
     * 线程池监控Bean
     */
    @Bean
    public AsyncExecutorMonitor asyncExecutorMonitor() {
        return new AsyncExecutorMonitor();
    }

    /**
     * 异步执行器监控类
     */
    public static class AsyncExecutorMonitor {
        
        /**
         * 获取导入线程池状态
         */
        public String getImportExecutorStatus() {
            // 这里可以添加线程池状态监控逻辑
            return "监控功能待实现";
        }
        
        /**
         * 获取线程池统计信息
         */
        public String getExecutorStatistics() {
            StringBuilder stats = new StringBuilder();
            stats.append("异步线程池统计信息:\n");
            stats.append("- 导入线程池: 核心线程数=").append(2).append(", 最大线程数=").append(5).append("\n");
            stats.append("- 队列容量: ").append(10).append("\n");
            stats.append("- 线程存活时间: ").append(60).append("秒\n");
            return stats.toString();
        }
        
        /**
         * 检查线程池健康状态
         */
        public boolean isHealthy() {
            // 这里可以添加健康检查逻辑
            // 例如检查线程池是否正常运行、队列是否过满等
            return true;
        }
        
        /**
         * 获取当前活跃线程数
         */
        public int getActiveThreadCount() {
            // 这里可以通过JMX或其他方式获取实际的活跃线程数
            return 0;
        }
        
        /**
         * 获取队列中等待的任务数
         */
        public int getQueueSize() {
            // 这里可以获取实际的队列大小
            return 0;
        }
        
        /**
         * 获取已完成的任务数
         */
        public long getCompletedTaskCount() {
            // 这里可以获取实际的已完成任务数
            return 0;
        }
        
        /**
         * 强制关闭所有任务（紧急情况使用）
         */
        public boolean forceShutdown() {
            try {
                log.warn("强制关闭异步线程池");
                // 这里可以添加强制关闭逻辑
                return true;
            } catch (Exception e) {
                log.error("强制关闭线程池失败", e);
                return false;
            }
        }
    }
}
