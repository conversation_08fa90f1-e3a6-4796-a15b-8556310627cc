package org.jeecg.config;

import com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.entity.ImportProgressInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * @Description: 导入进度Redis配置
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "async.import.enabled", havingValue = "true", matchIfMissing = true)
public class ImportProgressRedisConfig {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * 导入进度专用RedisTemplate
     * 使用现有的RedisConnectionFactory，确保与现有系统兼容
     */
    @Bean("importProgressRedisTemplate")
    public RedisTemplate<String, ImportProgressInfo> importProgressRedisTemplate() {
        RedisTemplate<String, ImportProgressInfo> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        
        // 使用String序列化器作为key的序列化器
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        
        // 使用FastJson序列化器作为value的序列化器
        GenericFastJsonRedisSerializer fastJsonRedisSerializer = new GenericFastJsonRedisSerializer();
        template.setValueSerializer(fastJsonRedisSerializer);
        template.setHashValueSerializer(fastJsonRedisSerializer);
        
        // 设置默认序列化器
        template.setDefaultSerializer(fastJsonRedisSerializer);
        
        template.afterPropertiesSet();
        
        log.info("导入进度RedisTemplate配置完成");
        return template;
    }

    /**
     * 验证Redis配置
     */
    @Bean
    public ImportProgressRedisValidator importProgressRedisValidator() {
        return new ImportProgressRedisValidator();
    }

    /**
     * Redis配置验证器
     */
    public static class ImportProgressRedisValidator {
        
        @Autowired
        private RedisTemplate<String, Object> redisTemplate;
        
        /**
         * 验证Redis连接和配置
         */
        public boolean validateRedisConfig() {
            try {
                // 测试基本连接
                redisTemplate.opsForValue().set("test:import:progress", "test", 10, java.util.concurrent.TimeUnit.SECONDS);
                String result = (String) redisTemplate.opsForValue().get("test:import:progress");
                
                if ("test".equals(result)) {
                    log.info("Redis配置验证成功");
                    // 清理测试数据
                    redisTemplate.delete("test:import:progress");
                    return true;
                } else {
                    log.error("Redis配置验证失败: 数据不一致");
                    return false;
                }
            } catch (Exception e) {
                log.error("Redis配置验证失败", e);
                return false;
            }
        }
        
        /**
         * 测试进度信息序列化
         */
        public boolean testProgressSerialization() {
            try {
                ImportProgressInfo testProgress = new ImportProgressInfo("test-task", "PROCESSING");
                testProgress.setTotalCount(100);
                testProgress.setProcessedCount(50);
                testProgress.setSuccessCount(45);
                testProgress.setFailureCount(5);
                testProgress.setCurrentMessage("测试序列化");
                
                String testKey = "test:progress:serialization";
                redisTemplate.opsForValue().set(testKey, testProgress, 10, java.util.concurrent.TimeUnit.SECONDS);
                
                Object result = redisTemplate.opsForValue().get(testKey);
                if (result instanceof ImportProgressInfo) {
                    ImportProgressInfo retrieved = (ImportProgressInfo) result;
                    boolean isValid = "test-task".equals(retrieved.getTaskId()) &&
                                    "PROCESSING".equals(retrieved.getStatus()) &&
                                    Integer.valueOf(100).equals(retrieved.getTotalCount());
                    
                    if (isValid) {
                        log.info("进度信息序列化测试成功");
                        redisTemplate.delete(testKey);
                        return true;
                    }
                }
                
                log.error("进度信息序列化测试失败: 数据不匹配");
                redisTemplate.delete(testKey);
                return false;
            } catch (Exception e) {
                log.error("进度信息序列化测试失败", e);
                return false;
            }
        }
    }
}
