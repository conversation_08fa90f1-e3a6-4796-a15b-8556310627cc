package org.jeecg.modules.reg.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.reg.service.TaskControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 任务控制控制器
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Api(tags = "任务控制管理")
@RestController
@RequestMapping("/reg/task-control")
@Slf4j
public class TaskControlController {

    @Autowired
    private TaskControlService taskControlService;

    /**
     * 暂停任务
     */
    @PostMapping("/pause/{taskId}")
    @ApiOperation(value = "暂停任务", notes = "暂停正在执行的导入任务")
    public Result<String> pauseTask(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "暂停原因") @RequestParam(value = "reason", defaultValue = "用户暂停") String reason) {
        
        try {
            // 权限检查
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            boolean success = taskControlService.pauseTask(taskId, reason);
            if (success) {
                log.info("任务暂停成功: taskId={}, user={}, reason={}", taskId, sysUser.getUsername(), reason);
                return Result.OK("任务已暂停");
            } else {
                return Result.error("暂停任务失败，请检查任务状态");
            }
        } catch (Exception e) {
            log.error("暂停任务失败: taskId={}", taskId, e);
            return Result.error("暂停任务失败: " + e.getMessage());
        }
    }

    /**
     * 恢复任务
     */
    @PostMapping("/resume/{taskId}")
    @ApiOperation(value = "恢复任务", notes = "恢复已暂停的导入任务")
    public Result<String> resumeTask(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        try {
            // 权限检查
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            boolean success = taskControlService.resumeTask(taskId);
            if (success) {
                log.info("任务恢复成功: taskId={}, user={}", taskId, sysUser.getUsername());
                return Result.OK("任务已恢复");
            } else {
                return Result.error("恢复任务失败，请检查任务状态");
            }
        } catch (Exception e) {
            log.error("恢复任务失败: taskId={}", taskId, e);
            return Result.error("恢复任务失败: " + e.getMessage());
        }
    }

    /**
     * 取消任务
     */
    @PostMapping("/cancel/{taskId}")
    @ApiOperation(value = "取消任务", notes = "取消导入任务")
    public Result<String> cancelTask(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "取消原因") @RequestParam(value = "reason", defaultValue = "用户取消") String reason) {
        
        try {
            // 权限检查
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            boolean success = taskControlService.cancelTask(taskId, reason);
            if (success) {
                log.info("任务取消成功: taskId={}, user={}, reason={}", taskId, sysUser.getUsername(), reason);
                return Result.OK("任务已取消");
            } else {
                return Result.error("取消任务失败，请检查任务状态");
            }
        } catch (Exception e) {
            log.error("取消任务失败: taskId={}", taskId, e);
            return Result.error("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 重试任务
     */
    @PostMapping("/retry/{taskId}")
    @ApiOperation(value = "重试任务", notes = "重新执行失败的导入任务")
    public Result<String> retryTask(
            @ApiParam(value = "原任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "重试原因") @RequestParam(value = "reason", defaultValue = "用户重试") String reason) {
        
        try {
            // 权限检查
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            String newTaskId = taskControlService.retryTask(taskId, reason);
            if (newTaskId != null) {
                log.info("任务重试成功: originalTaskId={}, newTaskId={}, user={}, reason={}", 
                        taskId, newTaskId, sysUser.getUsername(), reason);
                return Result.OK("重试任务已创建", newTaskId);
            } else {
                return Result.error("重试任务失败，请检查任务状态或原始文件是否存在");
            }
        } catch (Exception e) {
            log.error("重试任务失败: taskId={}", taskId, e);
            return Result.error("重试任务失败: " + e.getMessage());
        }
    }

    /**
     * 设置任务优先级
     */
    @PostMapping("/priority/{taskId}")
    @ApiOperation(value = "设置任务优先级", notes = "设置导入任务的执行优先级")
    public Result<String> setTaskPriority(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "优先级", required = true) @RequestParam("priority") String priority) {
        
        try {
            // 权限检查
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            // 验证优先级参数
            TaskControlService.TaskPriority taskPriority;
            try {
                taskPriority = TaskControlService.TaskPriority.valueOf(priority.toUpperCase());
            } catch (IllegalArgumentException e) {
                return Result.error("无效的优先级参数，支持的值: LOW, NORMAL, HIGH, URGENT");
            }
            
            boolean success = taskControlService.setTaskPriority(taskId, taskPriority);
            if (success) {
                log.info("任务优先级设置成功: taskId={}, priority={}, user={}", 
                        taskId, priority, sysUser.getUsername());
                return Result.OK("任务优先级已设置");
            } else {
                return Result.error("设置任务优先级失败");
            }
        } catch (Exception e) {
            log.error("设置任务优先级失败: taskId={}", taskId, e);
            return Result.error("设置优先级失败: " + e.getMessage());
        }
    }

    /**
     * 批量取消任务
     */
    @PostMapping("/batch-cancel")
    @ApiOperation(value = "批量取消任务", notes = "批量取消多个导入任务")
    public Result<JSONObject> batchCancelTasks(
            @ApiParam(value = "任务ID列表", required = true) @RequestParam("taskIds") String taskIds,
            @ApiParam(value = "取消原因") @RequestParam(value = "reason", defaultValue = "批量取消") String reason) {
        
        try {
            // 权限检查
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            // 解析任务ID列表
            List<String> taskIdList = Arrays.asList(taskIds.split(","));
            if (taskIdList.isEmpty()) {
                return Result.error("任务ID列表不能为空");
            }
            
            int successCount = taskControlService.batchCancelTasks(taskIdList, reason);
            
            JSONObject result = new JSONObject();
            result.put("totalCount", taskIdList.size());
            result.put("successCount", successCount);
            result.put("failureCount", taskIdList.size() - successCount);
            
            log.info("批量取消任务完成: total={}, success={}, user={}, reason={}", 
                    taskIdList.size(), successCount, sysUser.getUsername(), reason);
            
            return Result.OK(result);
        } catch (Exception e) {
            log.error("批量取消任务失败: taskIds={}", taskIds, e);
            return Result.error("批量取消失败: " + e.getMessage());
        }
    }

    /**
     * 强制停止任务
     */
    @PostMapping("/force-stop/{taskId}")
    @ApiOperation(value = "强制停止任务", notes = "强制停止导入任务（紧急情况使用）")
    public Result<String> forceStopTask(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            @ApiParam(value = "停止原因") @RequestParam(value = "reason", defaultValue = "强制停止") String reason) {
        
        try {
            // 权限检查（强制停止需要更高权限）
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            // 这里可以添加额外的权限检查，比如只有管理员才能强制停止
            // if (!isAdmin(sysUser)) {
            //     return Result.error("权限不足，只有管理员可以强制停止任务");
            // }
            
            boolean success = taskControlService.forceStopTask(taskId, reason);
            if (success) {
                log.warn("任务强制停止成功: taskId={}, user={}, reason={}", 
                        taskId, sysUser.getUsername(), reason);
                return Result.OK("任务已强制停止");
            } else {
                return Result.error("强制停止任务失败");
            }
        } catch (Exception e) {
            log.error("强制停止任务失败: taskId={}", taskId, e);
            return Result.error("强制停止失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务控制状态
     */
    @GetMapping("/status/{taskId}")
    @ApiOperation(value = "获取任务控制状态", notes = "获取任务的控制状态和可执行操作")
    public Result<JSONObject> getTaskControlStatus(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        try {
            JSONObject status = taskControlService.getTaskControlStatus(taskId);
            if (status.isEmpty()) {
                return Result.error("未找到任务控制状态");
            }
            return Result.OK(status);
        } catch (Exception e) {
            log.error("获取任务控制状态失败: taskId={}", taskId, e);
            return Result.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的优先级列表
     */
    @GetMapping("/priorities")
    @ApiOperation(value = "获取优先级列表", notes = "获取支持的任务优先级列表")
    public Result<JSONObject> getSupportedPriorities() {
        try {
            JSONObject priorities = new JSONObject();
            for (TaskControlService.TaskPriority priority : TaskControlService.TaskPriority.values()) {
                JSONObject priorityInfo = new JSONObject();
                priorityInfo.put("level", priority.getLevel());
                priorityInfo.put("description", priority.getDescription());
                priorities.put(priority.name(), priorityInfo);
            }
            return Result.OK(priorities);
        } catch (Exception e) {
            log.error("获取优先级列表失败", e);
            return Result.error("获取优先级列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务控制统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取控制统计", notes = "获取任务控制操作的统计信息")
    public Result<JSONObject> getControlStatistics() {
        try {
            // 这里可以添加统计信息的收集逻辑
            JSONObject statistics = new JSONObject();
            statistics.put("timestamp", System.currentTimeMillis());
            statistics.put("message", "统计功能待实现");
            
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取控制统计失败", e);
            return Result.error("获取统计失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期控制状态
     */
    @PostMapping("/cleanup")
    @ApiOperation(value = "清理过期状态", notes = "清理过期的任务控制状态（管理员功能）")
    public Result<String> cleanupExpiredStates() {
        try {
            // 权限检查
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            taskControlService.cleanupExpiredControlStates();
            
            log.info("清理过期控制状态完成: user={}", sysUser.getUsername());
            return Result.OK("清理完成");
        } catch (Exception e) {
            log.error("清理过期控制状态失败", e);
            return Result.error("清理失败: " + e.getMessage());
        }
    }
}
