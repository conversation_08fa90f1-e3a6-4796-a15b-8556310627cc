package org.jeecg.modules.reg.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.reg.entity.ImportProgressInfo;
import org.jeecg.modules.reg.service.AsyncImportService;
import org.jeecg.modules.reg.service.IImportTaskService;
import org.jeecg.modules.reg.service.ImportProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * @Description: 异步导入控制器
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Api(tags = "异步导入管理")
@RestController
@RequestMapping("/reg/async-import")
@Slf4j
public class AsyncImportController {

    @Autowired
    private AsyncImportService asyncImportService;
    
    @Autowired
    private ImportProgressService importProgressService;
    
    @Autowired
    private IImportTaskService importTaskService;

    /**
     * 异步导入Excel
     */
    @PostMapping("/excel")
    @ApiOperation(value = "异步导入Excel", notes = "异步导入Excel文件，支持大文件和实时进度反馈")
    public Result<String> importExcelAsync(
            @ApiParam(value = "Excel文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "单位登记ID", required = true) @RequestParam("companyRegId") String companyRegId,
            @ApiParam(value = "导入选项") @RequestParam(value = "options", required = false) String options,
            HttpServletRequest request) {
        
        try {
            // 参数验证
            if (file == null || file.isEmpty()) {
                return Result.error("上传文件不能为空");
            }
            
            if (companyRegId == null || companyRegId.trim().isEmpty()) {
                return Result.error("单位登记ID不能为空");
            }
            
            // 获取当前用户
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            // 文件格式验证
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(".xlsx") && 
                !fileName.toLowerCase().endsWith(".xls"))) {
                return Result.error("只支持Excel文件格式(.xlsx, .xls)");
            }
            
            // 文件大小限制（50MB）
            long maxFileSize = 50 * 1024 * 1024; // 50MB
            if (file.getSize() > maxFileSize) {
                return Result.error("文件大小不能超过50MB");
            }
            
            // 检查是否有正在处理的任务
            if (importTaskService.hasProcessingTask(companyRegId)) {
                return Result.error("该单位正在进行导入操作，请等待完成后再试");
            }
            
            // 生成任务ID
            String taskId = UUID.randomUUID().toString();
            
            // 创建导入任务记录
            String taskName = "客户登记导入 - " + fileName;
            String createdTaskId = importTaskService.createImportTask(
                taskName, 
                "customer_reg", 
                fileName, 
                file.getSize(), 
                companyRegId, 
                getCompanyName(companyRegId), 
                sysUser.getUsername()
            );
            
            if (createdTaskId == null) {
                return Result.error("创建导入任务失败");
            }
            
            // 使用创建的任务ID
            taskId = createdTaskId;
            
            // 初始化进度信息
            importProgressService.initProgress(taskId, 0, "正在解析Excel文件...");
            
            // 异步处理导入
            asyncImportService.processImportAsync(taskId, file, companyRegId, sysUser);
            
            log.info("异步导入任务已创建: taskId={}, fileName={}, companyRegId={}, user={}", 
                    taskId, fileName, companyRegId, sysUser.getUsername());
            
            return Result.OK("导入任务已创建，正在处理中", taskId);
            
        } catch (Exception e) {
            log.error("创建异步导入任务失败", e);
            return Result.error("创建导入任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取导入进度
     */
    @GetMapping("/progress/{taskId}")
    @ApiOperation(value = "获取导入进度", notes = "获取指定任务的导入进度信息")
    public Result<ImportProgressInfo> getImportProgress(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        try {
            ImportProgressInfo progressInfo = importProgressService.getProgress(taskId);
            if (progressInfo != null) {
                return Result.OK(progressInfo);
            } else {
                return Result.error("未找到任务进度信息");
            }
        } catch (Exception e) {
            log.error("获取导入进度失败: taskId={}", taskId, e);
            return Result.error("获取进度失败: " + e.getMessage());
        }
    }

    /**
     * 取消导入任务
     */
    @PostMapping("/cancel/{taskId}")
    @ApiOperation(value = "取消导入任务", notes = "取消正在进行的导入任务")
    public Result<String> cancelImportTask(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        try {
            // 检查任务是否可以取消
            if (!importTaskService.canCancelTask(taskId)) {
                return Result.error("任务状态不允许取消");
            }
            
            // 取消异步任务
            boolean cancelled = asyncImportService.cancelImportTask(taskId);
            if (cancelled) {
                // 更新任务状态
                importTaskService.cancelTask(taskId);
                // 更新进度信息
                importProgressService.cancelProgress(taskId, "用户取消");
                
                log.info("导入任务已取消: taskId={}", taskId);
                return Result.OK("任务已取消");
            } else {
                return Result.error("取消任务失败，任务可能已完成或不存在");
            }
        } catch (Exception e) {
            log.error("取消导入任务失败: taskId={}", taskId, e);
            return Result.error("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取导入结果详情
     */
    @GetMapping("/result/{taskId}")
    @ApiOperation(value = "获取导入结果", notes = "获取导入任务的详细结果信息")
    public Result<JSONObject> getImportResult(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        
        try {
            JSONObject result = asyncImportService.getImportResult(taskId);
            if (result != null) {
                return Result.OK(result);
            } else {
                return Result.error("未找到导入结果");
            }
        } catch (Exception e) {
            log.error("获取导入结果失败: taskId={}", taskId, e);
            return Result.error("获取结果失败: " + e.getMessage());
        }
    }

    /**
     * 下载错误报告
     */
    @GetMapping("/error-report/{taskId}")
    @ApiOperation(value = "下载错误报告", notes = "下载导入失败记录的Excel报告")
    public void downloadErrorReport(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId,
            HttpServletResponse response) {
        
        try {
            asyncImportService.downloadErrorReport(taskId, response);
            log.info("错误报告下载请求: taskId={}", taskId);
        } catch (Exception e) {
            log.error("下载错误报告失败: taskId={}", taskId, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("下载失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 重试导入任务
     */
    @PostMapping("/retry/{taskId}")
    @ApiOperation(value = "重试导入任务", notes = "重新执行失败的导入任务")
    public Result<String> retryImportTask(
            @ApiParam(value = "原任务ID", required = true) @PathVariable String taskId) {
        
        try {
            // 检查任务是否可以重试
            if (!importTaskService.canRetryTask(taskId)) {
                return Result.error("任务状态不允许重试");
            }
            
            // 获取当前用户
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            // 创建重试任务
            String newTaskId = asyncImportService.retryImportTask(taskId);
            if (newTaskId != null) {
                log.info("重试导入任务已创建: originalTaskId={}, newTaskId={}", taskId, newTaskId);
                return Result.OK("重试任务已创建", newTaskId);
            } else {
                return Result.error("创建重试任务失败");
            }
        } catch (Exception e) {
            log.error("重试导入任务失败: taskId={}", taskId, e);
            return Result.error("重试任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务历史列表
     */
    @GetMapping("/history")
    @ApiOperation(value = "获取任务历史", notes = "获取用户的导入任务历史列表")
    public Result<Object> getTaskHistory(
            @ApiParam(value = "单位登记ID") @RequestParam(value = "companyRegId", required = false) String companyRegId,
            @ApiParam(value = "限制数量") @RequestParam(value = "limit", defaultValue = "20") int limit) {
        
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                return Result.error("用户未登录");
            }
            
            if (companyRegId != null && !companyRegId.trim().isEmpty()) {
                // 获取指定单位的任务历史
                return Result.OK(importTaskService.getCompanyTaskHistory(companyRegId, limit));
            } else {
                // 获取用户的任务历史
                return Result.OK(importTaskService.getUserTaskHistory(sysUser.getUsername(), limit));
            }
        } catch (Exception e) {
            log.error("获取任务历史失败", e);
            return Result.error("获取历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取统计信息", notes = "获取导入任务的统计信息")
    public Result<Object> getTaskStatistics(
            @ApiParam(value = "单位登记ID") @RequestParam(value = "companyRegId", required = false) String companyRegId,
            @ApiParam(value = "开始时间") @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(value = "endTime", required = false) String endTime) {
        
        try {
            if (companyRegId == null || companyRegId.trim().isEmpty()) {
                return Result.error("单位登记ID不能为空");
            }
            
            // 设置默认时间范围（最近30天）
            if (startTime == null || startTime.trim().isEmpty()) {
                startTime = java.time.LocalDateTime.now().minusDays(30).toString();
            }
            if (endTime == null || endTime.trim().isEmpty()) {
                endTime = java.time.LocalDateTime.now().toString();
            }
            
            return Result.OK(importTaskService.getTaskStatistics(companyRegId, startTime, endTime));
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("获取统计失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @ApiOperation(value = "健康检查", notes = "检查异步导入服务的健康状态")
    public Result<Object> healthCheck() {
        try {
            JSONObject health = new JSONObject();
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());
            health.put("activeConnections", importProgressService.getActiveSessionCount());
            health.put("activeTasks", importProgressService.getActiveTaskCount());
            health.put("progressServiceHealth", importProgressService.healthCheck());
            
            return Result.OK(health);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return Result.error("健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取单位名称（辅助方法）
     */
    private String getCompanyName(String companyRegId) {
        // 这里应该调用相应的服务获取单位名称
        // 暂时返回默认值
        return "单位-" + companyRegId;
    }
}
