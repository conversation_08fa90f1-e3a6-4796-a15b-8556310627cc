-- ===================================================================
-- 任务1.2：创建导入任务管理基础设施 - 数据库表结构
-- 基于现有系统的命名规范和字段约定设计
-- ===================================================================

-- 导入任务表
CREATE TABLE `import_task` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_type` varchar(50) NOT NULL DEFAULT 'customer_reg' COMMENT '任务类型(customer_reg:客户登记导入)',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态(PENDING:等待中,PROCESSING:处理中,COMPLETED:已完成,FAILED:失败,CANCELLED:已取消)',
  `progress` int(3) DEFAULT '0' COMMENT '进度百分比(0-100)',
  `total_count` int(11) DEFAULT '0' COMMENT '总记录数',
  `processed_count` int(11) DEFAULT '0' COMMENT '已处理记录数',
  `success_count` int(11) DEFAULT '0' COMMENT '成功记录数',
  `failure_count` int(11) DEFAULT '0' COMMENT '失败记录数',
  `current_message` varchar(500) DEFAULT NULL COMMENT '当前处理信息',
  `error_message` text COMMENT '错误信息',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `company_reg_id` varchar(32) DEFAULT NULL COMMENT '单位登记ID',
  `company_name` varchar(200) DEFAULT NULL COMMENT '单位名称',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '耗时(毫秒)',
  `result_data` longtext COMMENT '结果数据(JSON格式)',
  `extra_params` text COMMENT '扩展参数(JSON格式)',
  PRIMARY KEY (`id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_status` (`status`),
  KEY `idx_company_reg_id` (`company_reg_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_create_by` (`create_by`),
  KEY `idx_composite` (`status`, `task_type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入任务表';

-- 导入任务详情表（可选，用于存储详细的导入记录）
CREATE TABLE `import_task_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `row_index` int(11) NOT NULL COMMENT '行号',
  `status` varchar(20) NOT NULL COMMENT '状态(SUCCESS:成功,FAILURE:失败)',
  `data_content` text COMMENT '数据内容(JSON格式)',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_row_index` (`row_index`),
  KEY `idx_composite` (`task_id`, `status`, `row_index`),
  CONSTRAINT `fk_import_task_detail_task_id` FOREIGN KEY (`task_id`) REFERENCES `import_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入任务详情表';

-- 插入测试数据（用于验证表结构）
INSERT INTO `import_task` (
  `id`, `task_name`, `task_type`, `status`, `progress`, 
  `total_count`, `processed_count`, `success_count`, `failure_count`,
  `current_message`, `file_name`, `file_size`, `company_reg_id`, `company_name`,
  `create_by`, `create_time`, `start_time`
) VALUES (
  'test-task-001', 
  '测试导入任务', 
  'customer_reg', 
  'COMPLETED', 
  100,
  100, 100, 95, 5,
  '导入完成',
  'test_data.xlsx',
  1024000,
  'company-001',
  '测试单位',
  'admin',
  NOW(),
  NOW()
);

-- 验证表结构和数据
SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_DEFAULT,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('import_task', 'import_task_detail')
ORDER BY TABLE_NAME, ORDINAL_POSITION;

-- 验证索引
SHOW INDEX FROM import_task;
SHOW INDEX FROM import_task_detail;

-- 验证测试数据
SELECT * FROM import_task WHERE id = 'test-task-001';
