package org.jeecg.config;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.handler.ImportProgressWebSocketHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

/**
 * @Description: WebSocket配置
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Slf4j
@Configuration
@EnableWebSocket
@ConditionalOnProperty(name = "async.import.enabled", havingValue = "true", matchIfMissing = true)
public class WebSocketConfig implements WebSocketConfigurer {

    @Value("${websocket.allowed-origins:*}")
    private String[] allowedOrigins;

    @Value("${websocket.max-text-message-size:65536}")
    private int maxTextMessageSize;

    @Value("${websocket.max-binary-message-size:65536}")
    private int maxBinaryMessageSize;

    @Value("${websocket.max-session-idle-timeout:300000}")
    private long maxSessionIdleTimeout;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册导入进度WebSocket处理器
        registry.addHandler(importProgressWebSocketHandler(), "/websocket/import-progress")
                .setAllowedOrigins(allowedOrigins)
                .withSockJS(); // 启用SockJS支持，提高兼容性
        
        log.info("WebSocket处理器注册完成: endpoint=/websocket/import-progress, allowedOrigins={}", 
                String.join(",", allowedOrigins));
    }

    /**
     * 导入进度WebSocket处理器
     */
    @Bean
    public ImportProgressWebSocketHandler importProgressWebSocketHandler() {
        return new ImportProgressWebSocketHandler();
    }

    /**
     * WebSocket容器配置
     */
    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        
        // 设置文本消息大小限制
        container.setMaxTextMessageBufferSize(maxTextMessageSize);
        
        // 设置二进制消息大小限制
        container.setMaxBinaryMessageBufferSize(maxBinaryMessageSize);
        
        // 设置会话空闲超时时间（5分钟）
        container.setMaxSessionIdleTimeout(maxSessionIdleTimeout);
        
        // 设置异步发送超时时间（30秒）
        container.setAsyncSendTimeout(30000L);
        
        log.info("WebSocket容器配置完成: maxTextMessageSize={}, maxBinaryMessageSize={}, maxSessionIdleTimeout={}ms", 
                maxTextMessageSize, maxBinaryMessageSize, maxSessionIdleTimeout);
        
        return container;
    }

    /**
     * WebSocket配置验证器
     */
    @Bean
    public WebSocketConfigValidator webSocketConfigValidator() {
        return new WebSocketConfigValidator();
    }

    /**
     * WebSocket配置验证器
     */
    public static class WebSocketConfigValidator {
        
        /**
         * 验证WebSocket配置
         */
        public boolean validateWebSocketConfig() {
            try {
                // 检查WebSocket相关的系统属性
                String javaVersion = System.getProperty("java.version");
                log.info("Java版本: {}", javaVersion);
                
                // 检查是否支持WebSocket
                try {
                    Class.forName("org.springframework.web.socket.WebSocketHandler");
                    log.info("WebSocket支持检查通过");
                    return true;
                } catch (ClassNotFoundException e) {
                    log.error("WebSocket支持检查失败: 缺少WebSocket相关类", e);
                    return false;
                }
            } catch (Exception e) {
                log.error("WebSocket配置验证失败", e);
                return false;
            }
        }
        
        /**
         * 获取WebSocket配置信息
         */
        public String getWebSocketConfigInfo() {
            StringBuilder info = new StringBuilder();
            info.append("WebSocket配置信息:\n");
            info.append("- Java版本: ").append(System.getProperty("java.version")).append("\n");
            info.append("- 操作系统: ").append(System.getProperty("os.name")).append("\n");
            info.append("- Spring Boot版本: ").append(getSpringBootVersion()).append("\n");
            return info.toString();
        }
        
        private String getSpringBootVersion() {
            try {
                Package pkg = org.springframework.boot.SpringBootVersion.class.getPackage();
                return pkg != null ? pkg.getImplementationVersion() : "未知";
            } catch (Exception e) {
                return "获取失败";
            }
        }
    }
}
