package org.jeecg.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PreDestroy;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Description: 增强的异步线程池配置
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Slf4j
@Configuration
@EnableAsync
@ConditionalOnProperty(name = "async.import.enabled", havingValue = "true", matchIfMissing = true)
public class EnhancedAsyncConfig {

    @Value("${async.import.executor.core-pool-size:2}")
    private int corePoolSize;

    @Value("${async.import.executor.max-pool-size:5}")
    private int maxPoolSize;

    @Value("${async.import.executor.queue-capacity:10}")
    private int queueCapacity;

    @Value("${async.import.executor.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    @Value("${async.import.executor.thread-name-prefix:Import-Async-}")
    private String threadNamePrefix;

    @Value("${async.import.executor.allow-core-thread-timeout:true}")
    private boolean allowCoreThreadTimeout;

    @Value("${async.import.executor.await-termination-seconds:60}")
    private int awaitTerminationSeconds;

    // 线程池实例，用于监控
    private ThreadPoolTaskExecutor importExecutor;

    /**
     * 增强的导入任务线程池
     */
    @Bean("importExecutor")
    public Executor importExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 基础配置
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setAllowCoreThreadTimeOut(allowCoreThreadTimeout);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        
        // 自定义拒绝策略
        executor.setRejectedExecutionHandler(new ImportTaskRejectedExecutionHandler());
        
        // 自定义线程工厂
        executor.setThreadFactory(new ImportTaskThreadFactory(threadNamePrefix));
        
        // 初始化
        executor.initialize();
        
        // 保存实例用于监控
        this.importExecutor = executor;
        
        log.info("增强导入线程池配置完成: core={}, max={}, queue={}, keepAlive={}s, allowCoreTimeout={}", 
                corePoolSize, maxPoolSize, queueCapacity, keepAliveSeconds, allowCoreThreadTimeout);
        
        return executor;
    }

    /**
     * 线程池监控器
     */
    @Bean
    public ImportExecutorMonitor importExecutorMonitor() {
        return new ImportExecutorMonitor();
    }

    /**
     * 线程池健康检查
     */
    @Bean
    public ImportExecutorHealthIndicator importExecutorHealthIndicator() {
        return new ImportExecutorHealthIndicator();
    }

    /**
     * 线程池性能统计
     */
    @Bean
    public ImportExecutorMetrics importExecutorMetrics() {
        return new ImportExecutorMetrics();
    }

    /**
     * 自定义拒绝策略
     */
    public static class ImportTaskRejectedExecutionHandler implements java.util.concurrent.RejectedExecutionHandler {
        private final AtomicLong rejectedCount = new AtomicLong(0);

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            long count = rejectedCount.incrementAndGet();
            log.warn("导入任务被拒绝执行: 拒绝次数={}, 活跃线程={}, 队列大小={}, 任务={}", 
                    count, executor.getActiveCount(), executor.getQueue().size(), r.getClass().getSimpleName());
            
            // 使用调用者线程执行（CallerRunsPolicy的行为）
            if (!executor.isShutdown()) {
                r.run();
            }
        }

        public long getRejectedCount() {
            return rejectedCount.get();
        }
    }

    /**
     * 自定义线程工厂
     */
    public static class ImportTaskThreadFactory implements java.util.concurrent.ThreadFactory {
        private final AtomicLong threadNumber = new AtomicLong(1);
        private final String namePrefix;

        public ImportTaskThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            thread.setPriority(Thread.NORM_PRIORITY);
            
            // 设置未捕获异常处理器
            thread.setUncaughtExceptionHandler((t, e) -> {
                log.error("导入线程发生未捕获异常: thread={}", t.getName(), e);
            });
            
            return thread;
        }
    }

    /**
     * 线程池监控器
     */
    public class ImportExecutorMonitor {
        
        /**
         * 获取线程池详细状态
         */
        public ThreadPoolStatus getThreadPoolStatus() {
            if (importExecutor == null) {
                return new ThreadPoolStatus();
            }
            
            ThreadPoolExecutor threadPool = importExecutor.getThreadPoolExecutor();
            ThreadPoolStatus status = new ThreadPoolStatus();
            
            status.setCorePoolSize(threadPool.getCorePoolSize());
            status.setMaximumPoolSize(threadPool.getMaximumPoolSize());
            status.setActiveCount(threadPool.getActiveCount());
            status.setPoolSize(threadPool.getPoolSize());
            status.setQueueSize(threadPool.getQueue().size());
            status.setCompletedTaskCount(threadPool.getCompletedTaskCount());
            status.setTaskCount(threadPool.getTaskCount());
            status.setLargestPoolSize(threadPool.getLargestPoolSize());
            status.setKeepAliveTime(threadPool.getKeepAliveTime(java.util.concurrent.TimeUnit.SECONDS));
            status.setShutdown(threadPool.isShutdown());
            status.setTerminated(threadPool.isTerminated());
            
            return status;
        }
        
        /**
         * 获取线程池使用率
         */
        public double getPoolUtilization() {
            if (importExecutor == null) {
                return 0.0;
            }
            
            ThreadPoolExecutor threadPool = importExecutor.getThreadPoolExecutor();
            int maxPoolSize = threadPool.getMaximumPoolSize();
            int activeCount = threadPool.getActiveCount();
            
            return maxPoolSize > 0 ? (double) activeCount / maxPoolSize : 0.0;
        }
        
        /**
         * 获取队列使用率
         */
        public double getQueueUtilization() {
            if (importExecutor == null) {
                return 0.0;
            }
            
            ThreadPoolExecutor threadPool = importExecutor.getThreadPoolExecutor();
            int queueCapacity = queueCapacity;
            int queueSize = threadPool.getQueue().size();
            
            return queueCapacity > 0 ? (double) queueSize / queueCapacity : 0.0;
        }
        
        /**
         * 动态调整核心线程数
         */
        public boolean adjustCorePoolSize(int newCorePoolSize) {
            try {
                if (importExecutor != null && newCorePoolSize > 0 && newCorePoolSize <= maxPoolSize) {
                    importExecutor.setCorePoolSize(newCorePoolSize);
                    log.info("动态调整核心线程数: {} -> {}", corePoolSize, newCorePoolSize);
                    return true;
                }
                return false;
            } catch (Exception e) {
                log.error("调整核心线程数失败", e);
                return false;
            }
        }
        
        /**
         * 动态调整最大线程数
         */
        public boolean adjustMaxPoolSize(int newMaxPoolSize) {
            try {
                if (importExecutor != null && newMaxPoolSize >= corePoolSize) {
                    importExecutor.setMaxPoolSize(newMaxPoolSize);
                    log.info("动态调整最大线程数: {} -> {}", maxPoolSize, newMaxPoolSize);
                    return true;
                }
                return false;
            } catch (Exception e) {
                log.error("调整最大线程数失败", e);
                return false;
            }
        }
    }

    /**
     * 线程池健康检查
     */
    public class ImportExecutorHealthIndicator implements HealthIndicator {
        
        @Override
        public Health health() {
            try {
                if (importExecutor == null) {
                    return Health.down().withDetail("reason", "线程池未初始化").build();
                }
                
                ThreadPoolExecutor threadPool = importExecutor.getThreadPoolExecutor();
                
                // 检查线程池是否关闭
                if (threadPool.isShutdown()) {
                    return Health.down().withDetail("reason", "线程池已关闭").build();
                }
                
                // 检查队列使用率
                double queueUtilization = importExecutorMonitor().getQueueUtilization();
                if (queueUtilization > 0.9) {
                    return Health.down()
                            .withDetail("reason", "队列使用率过高")
                            .withDetail("queueUtilization", queueUtilization)
                            .build();
                }
                
                // 检查线程池使用率
                double poolUtilization = importExecutorMonitor().getPoolUtilization();
                if (poolUtilization > 0.95) {
                    return Health.down()
                            .withDetail("reason", "线程池使用率过高")
                            .withDetail("poolUtilization", poolUtilization)
                            .build();
                }
                
                return Health.up()
                        .withDetail("activeThreads", threadPool.getActiveCount())
                        .withDetail("queueSize", threadPool.getQueue().size())
                        .withDetail("completedTasks", threadPool.getCompletedTaskCount())
                        .withDetail("poolUtilization", poolUtilization)
                        .withDetail("queueUtilization", queueUtilization)
                        .build();
                
            } catch (Exception e) {
                return Health.down().withException(e).build();
            }
        }
    }

    /**
     * 线程池性能统计
     */
    public class ImportExecutorMetrics {
        
        /**
         * 获取性能指标
         */
        public PerformanceMetrics getPerformanceMetrics() {
            PerformanceMetrics metrics = new PerformanceMetrics();
            
            if (importExecutor != null) {
                ThreadPoolExecutor threadPool = importExecutor.getThreadPoolExecutor();
                
                metrics.setActiveThreadCount(threadPool.getActiveCount());
                metrics.setPoolSize(threadPool.getPoolSize());
                metrics.setQueueSize(threadPool.getQueue().size());
                metrics.setCompletedTaskCount(threadPool.getCompletedTaskCount());
                metrics.setTaskCount(threadPool.getTaskCount());
                metrics.setLargestPoolSize(threadPool.getLargestPoolSize());
                
                // 计算吞吐量（任务/秒）
                long uptime = java.lang.management.ManagementFactory.getRuntimeMXBean().getUptime();
                double throughput = uptime > 0 ? (double) threadPool.getCompletedTaskCount() / (uptime / 1000.0) : 0.0;
                metrics.setThroughput(throughput);
                
                // 计算平均任务执行时间（估算）
                long totalTasks = threadPool.getTaskCount();
                if (totalTasks > 0) {
                    double avgExecutionTime = (double) uptime / totalTasks;
                    metrics.setAverageExecutionTime(avgExecutionTime);
                }
            }
            
            return metrics;
        }
    }

    /**
     * 线程池状态数据类
     */
    public static class ThreadPoolStatus {
        private int corePoolSize;
        private int maximumPoolSize;
        private int activeCount;
        private int poolSize;
        private int queueSize;
        private long completedTaskCount;
        private long taskCount;
        private int largestPoolSize;
        private long keepAliveTime;
        private boolean shutdown;
        private boolean terminated;

        // Getters and Setters
        public int getCorePoolSize() { return corePoolSize; }
        public void setCorePoolSize(int corePoolSize) { this.corePoolSize = corePoolSize; }
        
        public int getMaximumPoolSize() { return maximumPoolSize; }
        public void setMaximumPoolSize(int maximumPoolSize) { this.maximumPoolSize = maximumPoolSize; }
        
        public int getActiveCount() { return activeCount; }
        public void setActiveCount(int activeCount) { this.activeCount = activeCount; }
        
        public int getPoolSize() { return poolSize; }
        public void setPoolSize(int poolSize) { this.poolSize = poolSize; }
        
        public int getQueueSize() { return queueSize; }
        public void setQueueSize(int queueSize) { this.queueSize = queueSize; }
        
        public long getCompletedTaskCount() { return completedTaskCount; }
        public void setCompletedTaskCount(long completedTaskCount) { this.completedTaskCount = completedTaskCount; }
        
        public long getTaskCount() { return taskCount; }
        public void setTaskCount(long taskCount) { this.taskCount = taskCount; }
        
        public int getLargestPoolSize() { return largestPoolSize; }
        public void setLargestPoolSize(int largestPoolSize) { this.largestPoolSize = largestPoolSize; }
        
        public long getKeepAliveTime() { return keepAliveTime; }
        public void setKeepAliveTime(long keepAliveTime) { this.keepAliveTime = keepAliveTime; }
        
        public boolean isShutdown() { return shutdown; }
        public void setShutdown(boolean shutdown) { this.shutdown = shutdown; }
        
        public boolean isTerminated() { return terminated; }
        public void setTerminated(boolean terminated) { this.terminated = terminated; }
    }

    /**
     * 性能指标数据类
     */
    public static class PerformanceMetrics {
        private int activeThreadCount;
        private int poolSize;
        private int queueSize;
        private long completedTaskCount;
        private long taskCount;
        private int largestPoolSize;
        private double throughput;
        private double averageExecutionTime;

        // Getters and Setters
        public int getActiveThreadCount() { return activeThreadCount; }
        public void setActiveThreadCount(int activeThreadCount) { this.activeThreadCount = activeThreadCount; }
        
        public int getPoolSize() { return poolSize; }
        public void setPoolSize(int poolSize) { this.poolSize = poolSize; }
        
        public int getQueueSize() { return queueSize; }
        public void setQueueSize(int queueSize) { this.queueSize = queueSize; }
        
        public long getCompletedTaskCount() { return completedTaskCount; }
        public void setCompletedTaskCount(long completedTaskCount) { this.completedTaskCount = completedTaskCount; }
        
        public long getTaskCount() { return taskCount; }
        public void setTaskCount(long taskCount) { this.taskCount = taskCount; }
        
        public int getLargestPoolSize() { return largestPoolSize; }
        public void setLargestPoolSize(int largestPoolSize) { this.largestPoolSize = largestPoolSize; }
        
        public double getThroughput() { return throughput; }
        public void setThroughput(double throughput) { this.throughput = throughput; }
        
        public double getAverageExecutionTime() { return averageExecutionTime; }
        public void setAverageExecutionTime(double averageExecutionTime) { this.averageExecutionTime = averageExecutionTime; }
    }

    /**
     * 优雅关闭
     */
    @PreDestroy
    public void shutdown() {
        if (importExecutor != null) {
            log.info("开始关闭导入线程池...");
            importExecutor.shutdown();
            log.info("导入线程池关闭完成");
        }
    }
}
