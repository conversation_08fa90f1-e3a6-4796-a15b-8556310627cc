package org.jeecg.modules.reg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 导入任务
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Data
@TableName("import_task")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="import_task对象", description="导入任务")
public class ImportTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 任务名称
     */
    @Excel(name = "任务名称", width = 15)
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 任务类型（customer_reg: 客户登记导入）
     */
    @Excel(name = "任务类型", width = 15)
    @ApiModelProperty(value = "任务类型")
    private String taskType;

    /**
     * 任务状态（PENDING: 等待中, PROCESSING: 处理中, COMPLETED: 已完成, FAILED: 失败, CANCELLED: 已取消）
     */
    @Excel(name = "任务状态", width = 15)
    @ApiModelProperty(value = "任务状态")
    private String status;

    /**
     * 进度百分比（0-100）
     */
    @Excel(name = "进度", width = 15)
    @ApiModelProperty(value = "进度百分比")
    private Integer progress;

    /**
     * 总记录数
     */
    @Excel(name = "总记录数", width = 15)
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    /**
     * 已处理记录数
     */
    @Excel(name = "已处理记录数", width = 15)
    @ApiModelProperty(value = "已处理记录数")
    private Integer processedCount;

    /**
     * 成功记录数
     */
    @Excel(name = "成功记录数", width = 15)
    @ApiModelProperty(value = "成功记录数")
    private Integer successCount;

    /**
     * 失败记录数
     */
    @Excel(name = "失败记录数", width = 15)
    @ApiModelProperty(value = "失败记录数")
    private Integer failureCount;

    /**
     * 当前处理信息
     */
    @Excel(name = "当前处理信息", width = 30)
    @ApiModelProperty(value = "当前处理信息")
    private String currentMessage;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息", width = 50)
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 文件名
     */
    @Excel(name = "文件名", width = 30)
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @Excel(name = "文件大小", width = 15)
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 单位登记ID
     */
    @Excel(name = "单位登记ID", width = 15)
    @ApiModelProperty(value = "单位登记ID")
    private String companyRegId;

    /**
     * 单位名称
     */
    @Excel(name = "单位名称", width = 30)
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**
     * 开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 耗时（毫秒）
     */
    @Excel(name = "耗时", width = 15)
    @ApiModelProperty(value = "耗时")
    private Long duration;

    /**
     * 结果数据（JSON格式存储详细结果）
     */
    @ApiModelProperty(value = "结果数据")
    private String resultData;

    /**
     * 扩展参数（JSON格式）
     */
    @ApiModelProperty(value = "扩展参数")
    private String extraParams;

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("PENDING", "等待中"),
        PROCESSING("PROCESSING", "处理中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "失败"),
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String desc;

        TaskStatus(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 任务类型枚举
     */
    public enum TaskType {
        CUSTOMER_REG("customer_reg", "客户登记导入");

        private final String code;
        private final String desc;

        TaskType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 计算进度百分比
     */
    public void calculateProgress() {
        if (totalCount != null && totalCount > 0 && processedCount != null) {
            this.progress = (int) ((double) processedCount / totalCount * 100);
        } else {
            this.progress = 0;
        }
    }

    /**
     * 计算耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = endTime.getTime() - startTime.getTime();
        }
    }

    /**
     * 是否正在处理中
     */
    public boolean isProcessing() {
        return TaskStatus.PROCESSING.getCode().equals(this.status);
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return TaskStatus.COMPLETED.getCode().equals(this.status);
    }

    /**
     * 是否失败
     */
    public boolean isFailed() {
        return TaskStatus.FAILED.getCode().equals(this.status);
    }

    /**
     * 是否已取消
     */
    public boolean isCancelled() {
        return TaskStatus.CANCELLED.getCode().equals(this.status);
    }
}
