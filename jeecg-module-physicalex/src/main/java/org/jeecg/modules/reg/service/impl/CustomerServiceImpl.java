package org.jeecg.modules.reg.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mzlion.easyokhttp.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExApiConstants;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.jms.JmsMessageSender;
import org.jeecg.modules.basicinfo.service.ISequencesService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.reg.bo.HisPatient;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerMapper;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.service.ICustomerService;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.mapper.CustomerRegItemResultMapper;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 档案表
 * @Author: jeecg-boot
 * @Date: 2024-06-14
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer> implements ICustomerService {

    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private JmsMessageSender jmsMessageSender;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private ISequencesService sequencesService;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Autowired
    private CustomerRegItemResultMapper customerRegItemResultMapper;

    @Override
    public Customer saveCustomerByCustomerReg(CustomerReg customerReg) {
        //1、根据身份证号查询是否存在档案（处理可能存在多个记录的情况）
        Customer customer = null;
        try {
            customer = customerMapper.selectByIdCard(customerReg.getIdCard());
        } catch (Exception e) {
            // 如果查询返回多个结果，使用列表查询并取第一个
            log.warn("身份证号 {} 存在多条客户记录，将使用最新的记录", customerReg.getIdCard());
            List<Customer> customers = customerMapper.selectList(
                new LambdaQueryWrapper<Customer>()
                    .eq(Customer::getIdCard, customerReg.getIdCard())
                    .orderByDesc(Customer::getCreateTime)
                    .last("limit 1")
            );
            if (CollectionUtils.isNotEmpty(customers)) {
                customer = customers.get(0);
            }
        }

        //2、如果存在则更新档案信息，如果不存在则新增档案信息
        if (customer != null) {
            //如果customerAvatar是base64字符串，需要转换成图片保存
            customer.setAvatar(customerReg.getCustomerAvatar());
            customer.setName(customerReg.getName());
            customer.setPhone(customerReg.getPhone());
            customer.setIdCard(customerReg.getIdCard());
            customer.setGender(customerReg.getGender());
            customer.setBirthday(customerReg.getBirthday());
            customer.setNation(customerReg.getNation());
            customer.setAddress(customerReg.getAddress());
            customer.setMarriageStatus(customerReg.getMarriageStatus());
            customer.setAge(customerReg.getAge());
            customer.setAgeUnit(customerReg.getAgeUnit());
            customer.setWorkId(customerReg.getWorkType());
            customer.setPostCode(customerReg.getPostCode());
            customer.setPostStateId(customerReg.getPostCode());
            customer.setSecretLevel(customerReg.getSecretLevel());
            customer.setProvince(customerReg.getProvince());
            customer.setCity(customerReg.getCity());
            customer.setArea(customerReg.getArea());
            customer.setStreet(customerReg.getStreet());
            customer.setProvinceCode(customerReg.getProvinceCode());
            customer.setCityCode(customerReg.getCityCode());
            customer.setAreaCode(customerReg.getAreaCode());
            customer.setStreetCode(customerReg.getStreetCode());
            customer.setCountry(customerReg.getCountry());
            customer.setCountryCode(customerReg.getCountryCode());
            customer.setExamNo(customerReg.getExamNo());
            customer.setCardType(customerReg.getCardType());
            customer.setAddressDetail(customerReg.getAddressDetail());

            customerMapper.updateById(customer);
            try {
                sendCustomer2Mq(customer);
                updateCustomer2Interface(customer);
            } catch (Exception e) {
                log.error("消息队列：发送档案更新信息异常", e);
            }
        } else {
            customer = new Customer();
//            customer.setArchivesNum(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_ARCHIVE_NO)));
            customer.setArchivesNum(sequencesService.getNextSequenceWithPrefix(ExConstants.SEQ_ARCHIVE_NO));
            customer.setAvatar(customerReg.getCustomerAvatar());
            customer.setName(customerReg.getName());
            customer.setPhone(customerReg.getPhone());
            customer.setIdCard(customerReg.getIdCard());
            customer.setGender(customerReg.getGender());
            customer.setBirthday(customerReg.getBirthday());
            customer.setNation(customerReg.getNation());
            customer.setAddress(customerReg.getAddress());
            customer.setMarriageStatus(customerReg.getMarriageStatus());
            customer.setAge(customerReg.getAge());
            customer.setAgeUnit(customerReg.getAgeUnit());
            customer.setWorkId(customerReg.getWorkType());
            customer.setPostCode(customerReg.getPostCode());
            customer.setPostStateId(customerReg.getPostCode());
            customer.setSecretLevel(customerReg.getSecretLevel());
            customer.setCreateBy(customerReg.getCreatorBy());
            customer.setProvince(customerReg.getProvince());
            customer.setCity(customerReg.getCity());
            customer.setArea(customerReg.getArea());
            customer.setStreet(customerReg.getStreet());
            customer.setProvinceCode(customerReg.getProvinceCode());
            customer.setCityCode(customerReg.getCityCode());
            customer.setAreaCode(customerReg.getAreaCode());
            customer.setStreetCode(customerReg.getStreetCode());
            customer.setExamNo(customerReg.getExamNo());
            customer.setCardType(customerReg.getCardType());
            customer.setCountry(customerReg.getCountry());
            customer.setCountryCode(customerReg.getCountryCode());
            customer.setAddressDetail(customerReg.getAddressDetail());
            customerMapper.insert(customer);
            try {
                sendCustomer2Mq(customer);
                // HIS接口调用保持同步，确保能获取返回值并更新客户信息
                HisPatient hisPatient = addCustomer2Interface(customer);
                if (hisPatient != null) {
                    // 立即更新客户的HIS相关信息
                    customer.setHisPid(hisPatient.getPatientId());
                    customer.setIcCardNo(hisPatient.getIcCardNo());
                    customer.setHisHealthNo(hisPatient.getHealthNo());
                    customer.setInterfaceStatus("1");
                    // 更新数据库中的HIS信息
                    customerMapper.updateById(customer);
                    log.info("HIS建档成功，患者ID: {}, 健康档案号: {}", hisPatient.getPatientId(), hisPatient.getHealthNo());
                }else{
                    log.error("HIS建档返回数据为空,体检号：{}",customer.getExamNo());
                }
            } catch (Exception e) {
                log.error("HIS接口调用失败，将标记为待重试", e);
                // HIS调用失败时，标记状态但不影响主流程
                customer.setInterfaceStatus("0");
                // 注意：Customer实体类中没有interfaceErrorMsg字段，错误信息记录在日志中
                // 更新失败状态到数据库
                try {
                    customerMapper.updateById(customer);
                } catch (Exception updateEx) {
                    log.error("更新HIS失败状态异常", updateEx);
                }
            }
        }

        return customer;
    }

    @Override
    public Customer saveCustomer(Customer customer) throws Exception {

        customer.setArchivesNum(sequencesService.getNextSequenceWithPrefix(ExConstants.SEQ_ARCHIVE_NO));
        customerMapper.insert(customer);
        try {
            jmsMessageSender.sendArchiveTopicMessage(JSON.toJSONString(customer));
        } catch (Exception e) {
            log.error("消息队列：发送档案信息异常", e);
        }
        return customer;
    }

    @Override
    public Customer updateCustomer(Customer customer) throws Exception {
        customerMapper.updateById(customer);
        try {
            jmsMessageSender.updateArchiveTopicMessage(JSON.toJSONString(customer));
        } catch (Exception e) {
            log.error("消息队列：发送档案更新信息异常", e);
        }
        return customer;
    }

    @Override
    public Customer getByIdCard(String idCard) {
        QueryWrapper<Customer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id_card", idCard);
        queryWrapper.last("limit 1");
        return customerMapper.selectOne(queryWrapper);
    }

    @Override
    public void sendCustomer2Mq(Customer customer) throws Exception {
        try {
            String enable_jms_archive = sysSettingService.getValueByCode("enable_jms_archive");
            if (!StringUtils.equals(enable_jms_archive, "1")) {
                return;
            }
            if (StringUtils.isBlank(customer.getId())) {
                jmsMessageSender.sendArchiveTopicMessage(JSON.toJSONString(customer));
            } else {
                jmsMessageSender.updateArchiveTopicMessage(JSON.toJSONString(customer));
            }
        } catch (Exception e) {
            log.error("消息队列：发送档案信息异常", e);
        }
    }

    @Override
    public HisPatient addCustomer2Interface(Customer customer) throws Exception {
        String enable_his_patient_api = sysSettingService.getValueByCode("enable_his_patient_api");
        if (!StringUtils.equals(enable_his_patient_api, "1")) {
            return null;
        }

        String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        if (StringUtils.isBlank(hipInterfaceUrl)) {
            throw new Exception("HIP接口地址未配置");
        }
        hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");

        String finalUrl = hipInterfaceUrl + ExApiConstants.PATIENT_ADD_PATH;

        log.info("调用" + finalUrl + " 数据：" + JSONObject.toJSONString(customer));
        //String resultStr = HttpClientUtil.sendPost(finalUrl, JSON.toJSONString(customer));

        String resultStr = HttpClient.textBody(finalUrl).json(JSON.toJSONString(customer)).execute().asString();

        log.info("调用" + finalUrl + " 返回：" + resultStr);
        JSONObject resultJson = JSON.parseObject(resultStr);
        if (resultJson == null) {
            throw new Exception("HIP接口调用异常");
        }
        if (resultJson.getInteger("code") != 0) {
            throw new Exception("HIP接口调用异常，详情：" + resultJson.getString("msg"));
        }
        HisPatient hisPatient = JSONObject.parseObject(resultJson.getString("data"), HisPatient.class);

        // 移除内部数据库更新，让调用者负责更新数据库
        // 这样避免重复更新，并且让事务边界更清晰
        // LambdaUpdateWrapper<Customer> updateWrapper = new LambdaUpdateWrapper<>();
        // updateWrapper.set(Customer::getInterfaceStatus, "1").set(Customer::getHisPid, hisPatient.getPatientId()).set(Customer::getIcCardNo, hisPatient.getIcCardNo()).set(Customer::getHisHealthNo, hisPatient.getHealthNo()).eq(Customer::getId, customer.getId());
        // update(updateWrapper);

        return hisPatient;
    }

    @Override
    public HisPatient updateCustomer2Interface(Customer customer) throws Exception {
        String enable_his_patient_api = sysSettingService.getValueByCode("enable_his_patient_api");
        if (!StringUtils.equals(enable_his_patient_api, "1")) {
            return null;
        }

        String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        if (StringUtils.isBlank(hipInterfaceUrl)) {
            throw new Exception("HIP接口地址未配置");
        }
        hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");

        String finalUrl = hipInterfaceUrl + ExApiConstants.PATIENT_UPDATE_PATH;
        log.info("调用" + finalUrl + " 数据：" + JSON.toJSONString(customer));
        //String resultStr = HttpClientUtil.sendPost(finalUrl, JSON.toJSONString(customer));
        String resultStr = HttpClient.textBody(finalUrl).json(JSON.toJSONString(customer)).execute().asString();

        log.info("调用" + finalUrl + " 返回：" + resultStr);
        JSONObject resultJson = JSON.parseObject(resultStr);
        if (resultJson == null) {
            throw new Exception("HIP接口调用异常");
        }

        if (resultJson.getInteger("code") != 0) {
            throw new Exception("HIP接口调用异常，详情：" + resultJson.getString("msg"));
        }

        HisPatient hisPatient = JSONObject.parseObject(resultJson.getString("data"), HisPatient.class);
        LambdaUpdateWrapper<Customer> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Customer::getInterfaceStatus, "1").set(Customer::getHisPid, hisPatient.getPatientId()).set(Customer::getIcCardNo, hisPatient.getIcCardNo()).set(Customer::getHisHealthNo, hisPatient.getHealthNo()).eq(Customer::getId, customer.getId());
        update(updateWrapper);

        return hisPatient;
    }

    @Override
    public Map<String, Object> queryResultAndSummaryByRegId(String regId) {
        List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listWithItemGroupByReg(regId, null, false);
        //过滤掉已减项的和退款成功的记录
        groupList = groupList.stream().filter(group -> group.getAddMinusFlag() != -1 && !StringUtils.equals(group.getPayStatus(), ExConstants.REFUND_STATE_退款成功)).toList();
        List<CustomerRegItemResult> resultList = customerRegItemResultMapper.listByRegId(regId);
        //将resultList按照itemGroupId进行分组，如果checkPartCode不为空则按itemGroupId+checkPartCode分组
        Map<String, List<CustomerRegItemResult>> groupedByGroupIdResults = resultList.stream().collect(Collectors.groupingBy(result -> {
            String groupKey = result.getItemGroupId();
            // 如果checkPartCode不为空，则将其加入分组键
            if (StringUtils.isNotBlank(result.getCheckPartCode())) {
                groupKey = groupKey + "-" + result.getCheckPartCode();
            }
            return groupKey;
        }));
        groupList.forEach(group -> {
            // 收集该itemGroupId对应的所有结果，包括不同checkPartCode的结果
            List<CustomerRegItemResult> itemResultList = new ArrayList<>();

            // 先尝试获取没有checkPartCode的结果（原有逻辑）
            List<CustomerRegItemResult> baseResults = groupedByGroupIdResults.get(group.getItemGroupId());
            if (baseResults != null) {
                itemResultList.addAll(baseResults);
            }

            // 再获取有checkPartCode的结果
            groupedByGroupIdResults.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(group.getItemGroupId() + "-"))
                .forEach(entry -> itemResultList.addAll(entry.getValue()));

            group.setResultList(itemResultList.isEmpty() ? null : itemResultList);
            //如果resutList中有小项的abnormalFlag等于1，则将大项的abnormalFlag设置为1
            if (group.getResultList() != null && group.getResultList().stream().anyMatch(itemResult -> StringUtils.equals(itemResult.getAbnormalFlag(), "1"))) {
                group.setAbnormalFlag("1");
            }
        });

        CustomerRegSummary summary = customerRegSummaryMapper.getByRegId(regId);
        return Map.of("groupList", CollectionUtils.isNotEmpty(groupList) ? groupList : Lists.newArrayList(), "summary", Objects.nonNull(summary) ? summary : new CustomerRegSummary());
    }

    @Override
    public List<Customer> getFailedHisInterfaceCustomers() {
        // 查询HIS接口调用失败的客户记录
        // 条件：interfaceStatus为"0"且创建时间在24小时内
        return list(new LambdaQueryWrapper<Customer>()
            .eq(Customer::getInterfaceStatus, "0")
            .ge(Customer::getCreateTime, new java.util.Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000))
            .orderByDesc(Customer::getCreateTime)
            .last("limit 100")); // 限制最多100条记录
    }
}
