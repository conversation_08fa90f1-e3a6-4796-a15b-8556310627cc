package org.jeecg.modules.reg.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.config.CompanyRegCacheConfig;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * CompanyReg缓存管理服务
 * 实现多层缓存的统一管理和操作
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class CompanyRegCacheService {

    @Autowired
    @Qualifier(CompanyRegCacheConfig.LOCAL_CACHE_NAME)
    private Cache<String, Object> localCache;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CompanyRegCacheConfig cacheConfig;

    // ==================== 缓存查询操作 ====================

    /**
     * 从缓存中获取数据（多层缓存策略）
     * 1. 先查本地缓存
     * 2. 本地缓存未命中，查Redis缓存
     * 3. Redis缓存命中，回写本地缓存
     * 
     * @param keyword 搜索关键词
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param ids ID列表
     * @return 缓存的查询结果，未命中返回null
     */
    @SuppressWarnings("unchecked")
    public List<CompanyReg> getFromCache(String keyword, Integer pageNo, Integer pageSize, List<String> ids) {
        String cacheKey = CompanyRegCacheConfig.generateCacheKey(keyword, pageNo, pageSize, ids);
        
        try {
            // 1. 查询本地缓存
            if (cacheConfig.getStrategy().isLocalEnabled()) {
                Object localResult = localCache.getIfPresent(cacheKey);
                if (localResult != null) {
                    log.debug("本地缓存命中：{}", cacheKey);
                    return (List<CompanyReg>) localResult;
                }
            }

            // 2. 查询Redis缓存
            if (cacheConfig.getStrategy().isRedisEnabled()) {
                Object redisResult = redisTemplate.opsForValue().get(cacheKey);
                if (redisResult != null) {
                    log.debug("Redis缓存命中：{}", cacheKey);
                    
                    // 回写本地缓存
                    if (cacheConfig.getStrategy().isLocalEnabled()) {
                        localCache.put(cacheKey, redisResult);
                        log.debug("数据回写本地缓存：{}", cacheKey);
                    }
                    
                    return (List<CompanyReg>) redisResult;
                }
            }

            log.debug("缓存未命中：{}", cacheKey);
            return null;
            
        } catch (Exception e) {
            log.error("缓存查询异常：{}", cacheKey, e);
            return null;
        }
    }

    // ==================== 缓存写入操作 ====================

    /**
     * 将数据写入缓存（多层缓存策略）
     * 1. 写入本地缓存
     * 2. 写入Redis缓存
     * 
     * @param keyword 搜索关键词
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param ids ID列表
     * @param data 要缓存的数据
     */
    public void putToCache(String keyword, Integer pageNo, Integer pageSize, List<String> ids, List<CompanyReg> data) {
        if (data == null) {
            return;
        }

        String cacheKey = CompanyRegCacheConfig.generateCacheKey(keyword, pageNo, pageSize, ids);
        
        try {
            // 1. 写入本地缓存
            if (cacheConfig.getStrategy().isLocalEnabled()) {
                localCache.put(cacheKey, data);
                log.debug("数据写入本地缓存：{}，数据量：{}", cacheKey, data.size());
            }

            // 2. 写入Redis缓存
            if (cacheConfig.getStrategy().isRedisEnabled()) {
                redisTemplate.opsForValue().set(
                    cacheKey, 
                    data, 
                    cacheConfig.getRedis().getExpireTime(), 
                    TimeUnit.MINUTES
                );
                log.debug("数据写入Redis缓存：{}，数据量：{}，过期时间：{}分钟", 
                         cacheKey, data.size(), cacheConfig.getRedis().getExpireTime());
            }
            
        } catch (Exception e) {
            log.error("缓存写入异常：{}", cacheKey, e);
        }
    }

    // ==================== 缓存失效操作 ====================

    /**
     * 清除指定键的缓存
     * 
     * @param keyword 搜索关键词
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param ids ID列表
     */
    public void evictCache(String keyword, Integer pageNo, Integer pageSize, List<String> ids) {
        String cacheKey = CompanyRegCacheConfig.generateCacheKey(keyword, pageNo, pageSize, ids);
        evictCache(cacheKey);
    }

    /**
     * 清除指定键的缓存
     * 
     * @param cacheKey 缓存键
     */
    public void evictCache(String cacheKey) {
        try {
            // 清除本地缓存
            if (cacheConfig.getStrategy().isLocalEnabled()) {
                localCache.invalidate(cacheKey);
                log.debug("清除本地缓存：{}", cacheKey);
            }

            // 清除Redis缓存
            if (cacheConfig.getStrategy().isRedisEnabled()) {
                redisTemplate.delete(cacheKey);
                log.debug("清除Redis缓存：{}", cacheKey);
            }
            
        } catch (Exception e) {
            log.error("缓存清除异常：{}", cacheKey, e);
        }
    }

    /**
     * 批量清除相关缓存
     * 当CompanyReg数据发生变更时调用
     * 
     * @param keyword 相关的搜索关键词（可选）
     */
    public void evictRelatedCache(String keyword) {
        try {
            // 清除本地缓存（全部清除，因为Caffeine不支持模式匹配）
            if (cacheConfig.getStrategy().isLocalEnabled()) {
                localCache.invalidateAll();
                log.debug("清除所有本地缓存");
            }

            // 清除Redis缓存（支持模式匹配）
            if (cacheConfig.getStrategy().isRedisEnabled()) {
                String pattern = CompanyRegCacheConfig.generateKeyPattern(keyword);
                Set<String> keys = redisTemplate.keys(pattern);
                if (keys != null && !keys.isEmpty()) {
                    redisTemplate.delete(keys);
                    log.debug("批量清除Redis缓存：{}个键，模式：{}", keys.size(), pattern);
                }
            }
            
        } catch (Exception e) {
            log.error("批量缓存清除异常，关键词：{}", keyword, e);
        }
    }

    /**
     * 清除所有缓存
     */
    public void evictAllCache() {
        try {
            // 清除本地缓存
            if (cacheConfig.getStrategy().isLocalEnabled()) {
                localCache.invalidateAll();
                log.debug("清除所有本地缓存");
            }

            // 清除Redis缓存
            if (cacheConfig.getStrategy().isRedisEnabled()) {
                String pattern = CompanyRegCacheConfig.CACHE_PREFIX + "*";
                Set<String> keys = redisTemplate.keys(pattern);
                if (keys != null && !keys.isEmpty()) {
                    redisTemplate.delete(keys);
                    log.debug("清除所有Redis缓存：{}个键", keys.size());
                }
            }
            
        } catch (Exception e) {
            log.error("清除所有缓存异常", e);
        }
    }

    // ==================== 缓存统计和监控 ====================

    /**
     * 获取本地缓存统计信息
     */
    public CacheStats getLocalCacheStats() {
        return localCache.stats();
    }

    /**
     * 获取缓存命中率统计
     */
    public String getCacheStatsInfo() {
        if (!cacheConfig.getStrategy().isLocalEnabled()) {
            return "本地缓存未启用";
        }

        CacheStats stats = localCache.stats();
        return String.format(
            "本地缓存统计 - 命中率: %.2f%%, 命中次数: %d, 未命中次数: %d, 缓存大小: %d",
            stats.hitRate() * 100,
            stats.hitCount(),
            stats.missCount(),
            localCache.estimatedSize()
        );
    }

    /**
     * 预热缓存
     * 系统启动时预加载热点数据
     */
    public void preloadCache() {
        if (!cacheConfig.getStrategy().isPreloadEnabled()) {
            log.debug("缓存预热未启用");
            return;
        }

        try {
            log.info("开始预热CompanyReg缓存...");
            
            // 这里可以预加载一些热点查询
            // 例如：最近查询的关键词、常用的分页参数等
            // 具体实现可以根据业务需求调整
            
            log.info("CompanyReg缓存预热完成");
            
        } catch (Exception e) {
            log.error("缓存预热异常", e);
        }
    }
}
