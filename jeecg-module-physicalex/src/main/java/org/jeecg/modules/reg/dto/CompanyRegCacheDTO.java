package org.jeecg.modules.reg.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * CompanyReg缓存数据传输对象
 * 专门用于缓存的轻量级数据结构，只包含搜索结果需要的字段
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Accessors(chain = true)
public class CompanyRegCacheDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 缓存结果包装类
     */
    @Data
    @Accessors(chain = true)
    public static class CacheResult implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 搜索关键词
         */
        private String keyword;

        /**
         * 页码
         */
        private Integer pageNo;

        /**
         * 每页大小
         */
        private Integer pageSize;

        /**
         * 总记录数
         */
        private Long total;

        /**
         * 查询结果列表
         */
        private List<CompanyRegItem> records;

        /**
         * 缓存时间戳
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date cacheTime;

        /**
         * 缓存TTL（秒）
         */
        private Long ttl;

        /**
         * 数据版本号（用于缓存一致性检查）
         */
        private String version;
    }

    /**
     * CompanyReg缓存项
     * 只包含搜索结果需要的核心字段
     */
    @Data
    @Accessors(chain = true)
    public static class CompanyRegItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 主键ID
         */
        private String id;

        /**
         * 预约名称
         */
        private String regName;

        /**
         * 单位ID
         */
        private String companyId;

        /**
         * 单位名称
         */
        private String companyName;

        /**
         * 体检类别
         */
        private String examType;

        /**
         * 人员类别
         */
        private String personCategory;

        /**
         * 联系人
         */
        private String linkMan;

        /**
         * 开始日期
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date startCheckDate;

        /**
         * 结束日期
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date endCheckDate;

        /**
         * 预约人数
         */
        private Integer personCount;

        /**
         * 客服专员
         */
        private String serviceManager;

        /**
         * 结账状态
         */
        private String checkoutStatus;

        /**
         * 锁定状态
         */
        private String lockStatus;

        /**
         * 创建时间
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;

        /**
         * 创建人
         */
        private String createBy;

        /**
         * 更新时间
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date updateTime;

        /**
         * 更新人
         */
        private String updateBy;

        /**
         * 助记码（用于搜索优化）
         */
        private String helpChar;

        /**
         * 状态标识
         */
        private String status;
    }

    /**
     * 缓存统计信息
     */
    @Data
    @Accessors(chain = true)
    public static class CacheStats implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 本地缓存命中率
         */
        private Double localHitRate;

        /**
         * 本地缓存命中次数
         */
        private Long localHitCount;

        /**
         * 本地缓存未命中次数
         */
        private Long localMissCount;

        /**
         * 本地缓存大小
         */
        private Long localCacheSize;

        /**
         * Redis缓存键数量
         */
        private Long redisCacheKeyCount;

        /**
         * 总查询次数
         */
        private Long totalQueryCount;

        /**
         * 缓存节省的数据库查询次数
         */
        private Long savedDbQueryCount;

        /**
         * 平均响应时间（毫秒）
         */
        private Double avgResponseTime;

        /**
         * 统计时间
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date statsTime;
    }

    /**
     * 缓存配置信息
     */
    @Data
    @Accessors(chain = true)
    public static class CacheConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 本地缓存是否启用
         */
        private Boolean localEnabled;

        /**
         * Redis缓存是否启用
         */
        private Boolean redisEnabled;

        /**
         * 本地缓存最大大小
         */
        private Long localMaxSize;

        /**
         * 本地缓存写入后过期时间（分钟）
         */
        private Long localExpireAfterWrite;

        /**
         * 本地缓存访问后过期时间（分钟）
         */
        private Long localExpireAfterAccess;

        /**
         * Redis缓存过期时间（分钟）
         */
        private Long redisExpireTime;

        /**
         * 缓存预热是否启用
         */
        private Boolean preloadEnabled;

        /**
         * 预热数据大小
         */
        private Integer preloadSize;
    }

    /**
     * 缓存操作结果
     */
    @Data
    @Accessors(chain = true)
    public static class CacheOperationResult implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 操作是否成功
         */
        private Boolean success;

        /**
         * 操作类型（GET, PUT, EVICT, CLEAR）
         */
        private String operation;

        /**
         * 缓存键
         */
        private String cacheKey;

        /**
         * 操作耗时（毫秒）
         */
        private Long duration;

        /**
         * 是否命中缓存
         */
        private Boolean hit;

        /**
         * 缓存层级（LOCAL, REDIS, DATABASE）
         */
        private String cacheLevel;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 操作时间
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date operationTime;
    }
}
