package org.jeecg.modules.reg.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CompanyReg缓存功能测试
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class CompanyRegCacheTest {

    @Autowired
    private ICompanyRegService companyRegService;

    @Autowired
    private CacheManager cacheManager;

    /**
     * 测试缓存基本功能
     */
    @Test
    public void testCacheBasicFunction() {
        log.info("开始测试缓存基本功能");

        String keyword = "测试公司";
        Integer pageNo = 1;
        Integer pageSize = 10;
        List<String> ids = Arrays.asList("1", "2", "3");

        // 第一次查询 - 应该查询数据库
        long startTime1 = System.currentTimeMillis();
        List<CompanyReg> result1 = companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, ids);
        long duration1 = System.currentTimeMillis() - startTime1;
        log.info("第一次查询耗时: {}ms, 结果数量: {}", duration1, result1.size());

        // 第二次查询 - 应该命中缓存
        long startTime2 = System.currentTimeMillis();
        List<CompanyReg> result2 = companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, ids);
        long duration2 = System.currentTimeMillis() - startTime2;
        log.info("第二次查询耗时: {}ms, 结果数量: {}", duration2, result2.size());

        // 验证结果一致性
        assertEquals(result1.size(), result2.size(), "两次查询结果数量应该一致");
        
        // 缓存命中应该更快（通常小于10ms）
        assertTrue(duration2 < duration1, "缓存命中应该比数据库查询更快");
        
        log.info("缓存基本功能测试通过");
    }

    /**
     * 测试不同参数的缓存隔离
     */
    @Test
    public void testCacheIsolation() {
        log.info("开始测试缓存隔离功能");

        // 不同关键词
        List<CompanyReg> result1 = companyRegService.searchCompanyRegWithCache("公司A", 1, 10, null);
        List<CompanyReg> result2 = companyRegService.searchCompanyRegWithCache("公司B", 1, 10, null);
        
        // 不同分页参数
        List<CompanyReg> result3 = companyRegService.searchCompanyRegWithCache("公司A", 1, 10, null);
        List<CompanyReg> result4 = companyRegService.searchCompanyRegWithCache("公司A", 2, 10, null);
        
        // 不同ID列表
        List<CompanyReg> result5 = companyRegService.searchCompanyRegWithCache("公司A", 1, 10, Arrays.asList("1", "2"));
        List<CompanyReg> result6 = companyRegService.searchCompanyRegWithCache("公司A", 1, 10, Arrays.asList("3", "4"));

        log.info("不同参数查询结果: {}, {}, {}, {}, {}, {}", 
                result1.size(), result2.size(), result3.size(), 
                result4.size(), result5.size(), result6.size());

        // 验证相同参数的查询结果一致
        List<CompanyReg> result3Again = companyRegService.searchCompanyRegWithCache("公司A", 1, 10, null);
        assertEquals(result3.size(), result3Again.size(), "相同参数的查询结果应该一致");

        log.info("缓存隔离功能测试通过");
    }

    /**
     * 测试缓存失效功能
     */
    @Test
    public void testCacheEviction() {
        log.info("开始测试缓存失效功能");

        String keyword = "测试缓存失效";
        Integer pageNo = 1;
        Integer pageSize = 10;

        // 第一次查询，建立缓存
        List<CompanyReg> result1 = companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, null);
        log.info("建立缓存，查询结果数量: {}", result1.size());

        // 验证缓存存在
        assertNotNull(cacheManager.getCache("companyRegCache"), "缓存应该存在");

        // 清除缓存
        if (companyRegService instanceof org.jeecg.modules.reg.service.impl.CompanyRegServiceImpl) {
            ((org.jeecg.modules.reg.service.impl.CompanyRegServiceImpl) companyRegService).evictCompanyRegCache();
        }

        // 再次查询，应该重新查询数据库
        long startTime = System.currentTimeMillis();
        List<CompanyReg> result2 = companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, null);
        long duration = System.currentTimeMillis() - startTime;
        
        log.info("缓存清除后查询耗时: {}ms, 结果数量: {}", duration, result2.size());

        // 验证结果一致性
        assertEquals(result1.size(), result2.size(), "缓存清除前后查询结果应该一致");

        log.info("缓存失效功能测试通过");
    }

    /**
     * 测试空结果缓存
     */
    @Test
    public void testEmptyResultCache() {
        log.info("开始测试空结果缓存");

        String keyword = "不存在的公司名称_" + System.currentTimeMillis();
        Integer pageNo = 1;
        Integer pageSize = 10;

        // 第一次查询空结果
        long startTime1 = System.currentTimeMillis();
        List<CompanyReg> result1 = companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, null);
        long duration1 = System.currentTimeMillis() - startTime1;
        log.info("第一次查询空结果耗时: {}ms, 结果数量: {}", duration1, result1.size());

        // 第二次查询空结果 - 应该命中缓存
        long startTime2 = System.currentTimeMillis();
        List<CompanyReg> result2 = companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, null);
        long duration2 = System.currentTimeMillis() - startTime2;
        log.info("第二次查询空结果耗时: {}ms, 结果数量: {}", duration2, result2.size());

        // 验证空结果
        assertTrue(result1.isEmpty(), "第一次查询应该返回空结果");
        assertTrue(result2.isEmpty(), "第二次查询应该返回空结果");
        assertEquals(result1.size(), result2.size(), "两次空查询结果应该一致");

        // 缓存命中应该更快
        assertTrue(duration2 < duration1, "空结果缓存命中应该更快");

        log.info("空结果缓存测试通过");
    }

    /**
     * 测试缓存配置
     */
    @Test
    public void testCacheConfiguration() {
        log.info("开始测试缓存配置");

        // 验证缓存管理器存在
        assertNotNull(cacheManager, "缓存管理器应该存在");

        // 验证缓存存在
        assertNotNull(cacheManager.getCache("companyRegCache"), "companyRegCache缓存应该存在");

        log.info("缓存管理器类型: {}", cacheManager.getClass().getSimpleName());
        log.info("可用缓存名称: {}", cacheManager.getCacheNames());

        log.info("缓存配置测试通过");
    }

    /**
     * 性能基准测试
     */
    @Test
    public void testPerformanceBenchmark() {
        log.info("开始性能基准测试");

        String keyword = "性能测试";
        Integer pageNo = 1;
        Integer pageSize = 10;

        int testRounds = 10;
        long totalDbTime = 0;
        long totalCacheTime = 0;

        // 预热
        companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, null);

        for (int i = 0; i < testRounds; i++) {
            // 清除缓存，测试数据库查询时间
            if (companyRegService instanceof org.jeecg.modules.reg.service.impl.CompanyRegServiceImpl) {
                ((org.jeecg.modules.reg.service.impl.CompanyRegServiceImpl) companyRegService).evictCompanyRegCache();
            }

            long startTime = System.currentTimeMillis();
            companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, null);
            totalDbTime += (System.currentTimeMillis() - startTime);

            // 测试缓存查询时间
            startTime = System.currentTimeMillis();
            companyRegService.searchCompanyRegWithCache(keyword, pageNo, pageSize, null);
            totalCacheTime += (System.currentTimeMillis() - startTime);
        }

        double avgDbTime = totalDbTime / (double) testRounds;
        double avgCacheTime = totalCacheTime / (double) testRounds;
        double speedupRatio = avgDbTime / avgCacheTime;

        log.info("性能测试结果 ({} 轮):", testRounds);
        log.info("平均数据库查询时间: {:.2f}ms", avgDbTime);
        log.info("平均缓存查询时间: {:.2f}ms", avgCacheTime);
        log.info("性能提升倍数: {:.2f}x", speedupRatio);

        // 验证缓存确实提升了性能
        assertTrue(speedupRatio > 1.0, "缓存应该提升查询性能");

        log.info("性能基准测试完成");
    }
}
