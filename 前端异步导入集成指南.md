# 前端异步导入功能集成指南

## 🎯 **功能概述**

基于Spring事件驱动的异步导入功能，通过WebSocket实时推送进度，提供完整的用户体验。

## 📁 **已创建的前端文件**

```
src/
├── utils/
│   └── websocket.ts                    # WebSocket连接管理器
├── api/
│   └── asyncImport.ts                  # 异步导入API接口
├── components/ImportProgress/
│   └── AsyncImportModal.vue            # 异步导入进度弹窗
└── views/reg/components/
    └── AsyncImportButton.vue           # 异步导入按钮组件
```

## 🚀 **集成步骤**

### 1. 在现有页面中使用异步导入按钮

在您的登记列表页面（如 `CustomerRegList.vue`）中集成：

```vue
<template>
  <div>
    <!-- 现有的页面内容 -->
    <div class="table-page-search-submitButtons">
      <!-- 现有按钮 -->
      <a-button type="primary" @click="handleAdd">新增</a-button>
      
      <!-- 新增：异步导入按钮 -->
      <AsyncImportButton
        :company-reg-id="selectedCompanyId"
        @success="handleImportSuccess"
        @error="handleImportError"
      />
    </div>
    
    <!-- 现有的表格等内容 -->
  </div>
</template>

<script lang="ts" setup>
import AsyncImportButton from './components/AsyncImportButton.vue';

// 现有的代码...

// 处理导入成功
const handleImportSuccess = (result: any) => {
  console.log('导入成功:', result);
  
  // 刷新表格数据
  reload();
  
  // 显示成功消息
  message.success(`导入完成！成功: ${result.successCount}, 失败: ${result.failureCount}`);
};

// 处理导入错误
const handleImportError = (error: any) => {
  console.error('导入失败:', error);
  message.error('导入失败，请重试');
};
</script>
```

### 2. 直接使用异步导入API

如果您需要自定义导入逻辑：

```vue
<script lang="ts" setup>
import { asyncImportExcel, getImportProgress } from '/@/api/asyncImport';
import { importProgressWebSocket } from '/@/utils/websocket';

const handleCustomImport = async (file: File, companyRegId: string) => {
  try {
    // 1. 启动异步导入
    const result = await asyncImportExcel({
      file,
      companyRegId,
    });
    
    if (result.success) {
      const taskId = result.result;
      
      // 2. 连接WebSocket监听进度
      await importProgressWebSocket.connect();
      
      // 3. 订阅进度更新
      const subscriptionId = importProgressWebSocket.subscribeTaskProgress(taskId, (progress) => {
        console.log('进度更新:', progress);
        
        // 更新UI
        updateProgressUI(progress);
        
        // 处理完成事件
        if (progress.eventType === 'COMPLETE') {
          message.success('导入完成！');
          reload(); // 刷新数据
        }
      });
      
      message.success('导入任务已创建');
    }
  } catch (error) {
    message.error('创建导入任务失败');
  }
};
</script>
```

### 3. 使用进度弹窗组件

```vue
<template>
  <div>
    <a-button @click="startImport">开始导入</a-button>
    
    <AsyncImportModal
      v-model:visible="showProgress"
      :task-id="currentTaskId"
      @success="handleSuccess"
      @cancel="handleCancel"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import AsyncImportModal from '/@/components/ImportProgress/AsyncImportModal.vue';

const showProgress = ref(false);
const currentTaskId = ref('');

const startImport = async () => {
  // 启动导入并获取taskId
  const taskId = await startAsyncImport();
  
  // 显示进度弹窗
  currentTaskId.value = taskId;
  showProgress.value = true;
};
</script>
```

## 🔧 **配置说明**

### 1. 安装依赖

确保项目中已安装WebSocket相关依赖：

```bash
npm install sockjs-client @stomp/stompjs
```

### 2. 类型定义

在 `types/` 目录下添加类型定义：

```typescript
// types/websocket.d.ts
declare module 'sockjs-client' {
  export default class SockJS {
    constructor(url: string, protocols?: string | string[], options?: any);
  }
}
```

### 3. 环境配置

在 `.env` 文件中配置WebSocket地址：

```env
# 开发环境
VITE_WEBSOCKET_URL=ws://localhost:8080/ws/import-progress

# 生产环境
VITE_WEBSOCKET_URL=wss://your-domain.com/ws/import-progress
```

## 📊 **API接口说明**

### 1. 异步导入接口

```typescript
POST /reg/async-import-v2/import

// 请求参数
{
  file: File,           // Excel文件
  companyRegId: string  // 单位ID
}

// 响应
{
  success: true,
  result: "taskId123",  // 任务ID
  message: "导入任务已创建"
}
```

### 2. 获取进度接口

```typescript
GET /reg/async-import-v2/progress/{taskId}

// 响应
{
  success: true,
  result: {
    taskId: "taskId123",
    eventType: "PROGRESS",
    totalCount: 100,
    processedCount: 50,
    successCount: 45,
    failureCount: 5,
    progress: 50,
    message: "正在处理第50/100条记录",
    timestamp: 1640995200000
  }
}
```

## 🌐 **WebSocket事件**

### 1. 连接地址

```
/ws/import-progress
```

### 2. 订阅频道

```
/topic/import-progress/{taskId}  # 特定任务进度
/topic/import-progress           # 通用进度频道
```

### 3. 消息格式

```json
{
  "taskId": "taskId123",
  "type": "PROGRESS",
  "totalCount": 100,
  "processedCount": 50,
  "successCount": 45,
  "failureCount": 5,
  "progress": 50,
  "message": "正在处理第50/100条记录",
  "timestamp": 1640995200000
}
```

## 🎨 **UI组件特性**

### AsyncImportModal 组件特性：

- ✅ 实时进度显示
- ✅ 统计信息展示（总计/已处理/成功/失败）
- ✅ 进度条和状态图标
- ✅ 实时日志显示
- ✅ 错误信息展示
- ✅ 取消导入功能
- ✅ 自动清理资源

### AsyncImportButton 组件特性：

- ✅ 文件类型验证
- ✅ 文件大小限制
- ✅ 自动显示进度弹窗
- ✅ 成功/失败事件回调
- ✅ 加载状态显示

## 🔍 **使用示例**

### 在CustomerRegList.vue中集成：

```vue
<template>
  <div>
    <!-- 工具栏 -->
    <div class="table-page-search-submitButtons">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <PlusOutlined />新增
        </a-button>
        
        <!-- 异步导入按钮 -->
        <AsyncImportButton
          :company-reg-id="queryParam.companyRegId"
          @success="handleAsyncImportSuccess"
          @error="handleAsyncImportError"
        />
        
        <a-button type="primary" @click="handleExportXls">
          <DownloadOutlined />导出
        </a-button>
      </a-space>
    </div>
    
    <!-- 表格 -->
    <BasicTable @register="registerTable" />
  </div>
</template>

<script lang="ts" setup>
import AsyncImportButton from './components/AsyncImportButton.vue';

// 处理异步导入成功
const handleAsyncImportSuccess = (result: any) => {
  // 刷新表格
  reload();
  
  // 显示结果
  Modal.success({
    title: '导入完成',
    content: `成功导入 ${result.successCount} 条记录，失败 ${result.failureCount} 条记录`,
  });
};

// 处理异步导入错误
const handleAsyncImportError = (error: any) => {
  console.error('异步导入失败:', error);
};
</script>
```

## 🎯 **核心优势**

1. **真正异步**: 不阻塞用户操作，可以继续使用系统其他功能
2. **实时反馈**: WebSocket实时推送进度，用户体验良好
3. **事件驱动**: 基于Spring事件机制，解耦合设计
4. **易于集成**: 组件化设计，一行代码即可集成
5. **完整功能**: 支持取消、错误处理、日志查看等完整功能

现在您可以在任何需要导入功能的页面中轻松集成异步导入功能！
