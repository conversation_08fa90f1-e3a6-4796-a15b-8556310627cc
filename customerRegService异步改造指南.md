# CustomerRegService 异步改造指南

## 🎯 **改造目标**

在现有的`customerRegService.importExcel`方法中添加进度事件发布，通过Spring事件机制实现异步进度推送。

## 📋 **改造步骤**

### 1. 在CustomerRegServiceImpl中注入事件发布器

```java
@Autowired
private ApplicationEventPublisher eventPublisher;
```

### 2. 修改importExcel方法，添加进度事件发布

在现有的`importExcel`方法中的关键位置添加事件发布：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
    // 获取异步任务ID（如果是异步调用）
    String taskId = (String) request.getAttribute("asyncTaskId");
    boolean isAsync = taskId != null;
    
    // 获取单位登记ID用于构建锁键
    String companyRegId = request.getParameter("companyRegId");
    if (StringUtils.isBlank(companyRegId)) {
        return Result.error("单位登记ID不能为空");
    }

    // 构建导入级分布式锁
    String importLockKey = "IMPORT_EXCEL_LOCK:" + companyRegId;
    RLock importLock = redissonClient.getLock(importLockKey);

    try {
        // 尝试获取锁，最多等待30秒，锁定10分钟
        boolean lockAcquired = importLock.tryLock(30, 600, TimeUnit.SECONDS);
        if (!lockAcquired) {
            log.warn("导入操作获取锁失败，单位ID: {}", companyRegId);
            return Result.error("系统繁忙，该单位正在进行导入操作，请稍后重试");
        }

        log.info("开始导入Excel，单位ID: {}, 锁键: {}", companyRegId, importLockKey);
        
        // 🔥 发布开始事件
        if (isAsync) {
            eventPublisher.publishEvent(ImportProgressEvent.start(this, taskId, 0, "开始解析Excel文件..."));
        }
        
        return doImportExcel(request, response, companyRegId, taskId, isAsync);

    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        log.error("导入操作被中断，单位ID: {}", companyRegId, e);
        
        // 🔥 发布失败事件
        if (isAsync) {
            eventPublisher.publishEvent(ImportProgressEvent.failed(this, taskId, "导入操作被中断"));
        }
        
        return Result.error("导入操作被中断，请重试");
    } finally {
        // 确保释放锁
        if (importLock.isHeldByCurrentThread()) {
            importLock.unlock();
            log.info("释放导入锁，单位ID: {}", companyRegId);
        }
    }
}
```

### 3. 修改doImportExcel方法，添加进度跟踪

```java
private Result<?> doImportExcel(HttpServletRequest request, HttpServletResponse response, 
                               String companyRegId, String taskId, boolean isAsync) throws Exception {
    LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
    CompanyReg companyReg = companyRegMapper.selectById(companyRegId);

    if (companyReg == null) {
        return Result.error("单位不存在！");
    }
    
    List<CompanyTeam> companyTeams = companyTeamMapper.selectByMainId(companyReg.getId());
    if (CollectionUtils.isEmpty(companyTeams)) {
        return Result.error("该单位下未设置分组！");
    }

    // 🔥 发布进度事件：开始解析文件
    if (isAsync) {
        eventPublisher.publishEvent(new ImportProgressEvent(this, taskId, 0, 0, 0, 0, "正在解析Excel文件..."));
    }

    // 解析Excel文件
    MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
    Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
    
    List<CustomerReg> successList = new ArrayList<>();
    List<CustomerReg> failureList = new ArrayList<>();
    List<JSONObject> failureResults = new ArrayList<>();
    
    int totalCount = 0;
    
    for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
        MultipartFile file = entity.getValue();
        ImportParams params = new ImportParams();
        params.setTitleRows(2);
        params.setHeadRows(1);
        params.setNeedSave(true);

        List<CustomerReg> list;
        try (InputStream inputStream = file.getInputStream()) {
            list = ExcelImportUtil.importExcel(inputStream, CustomerReg.class, params);
        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            continue;
        }
        
        totalCount += list.size();
        
        // 🔥 发布进度事件：文件解析完成
        if (isAsync) {
            eventPublisher.publishEvent(new ImportProgressEvent(this, taskId, totalCount, 0, 0, 0, 
                String.format("文件解析完成，共 %d 条记录，开始处理数据...", totalCount)));
        }
        
        // 批量处理优化：预处理和批量验证
        BatchProcessResult batchProcessResult = batchProcessCustomerRegsWithProgress(
            list, companyReg, companyTeams, sysUser, importWithRateLimit, taskId, isAsync);

        successList.addAll(batchProcessResult.getSuccessList());
        failureList.addAll(batchProcessResult.getFailureList());
        failureResults.addAll(batchProcessResult.getFailureResults());
    }

    try {
        // 🔥 发布进度事件：开始保存数据
        if (isAsync) {
            eventPublisher.publishEvent(new ImportProgressEvent(this, taskId, totalCount, 
                successList.size(), successList.size(), failureList.size(), "正在保存数据到数据库..."));
        }
        
        // 先清除该单位的历史错误记录
        clearHistoryImportRecords(companyRegId, successList);

        // 预分配序列号范围，减少数据库锁竞争
        preAllocateSequenceRange(companyRegId, successList.size());

        // 批量保存成功记录
        if (!successList.isEmpty()) {
            this.saveBatch(successList);
            log.info("批量保存成功记录: 单位ID={}, 成功记录数={}", companyRegId, successList.size());
        }

        // 保存错误记录
        if (!failureResults.isEmpty()) {
            List<CompanyImportRecord> records = failureResults.stream()
                .map(failureResult -> {
                    CompanyImportRecord record = new CompanyImportRecord();
                    record.setCompanyRegId(companyRegId);
                    record.setErrorData(failureResult.toJSONString());
                    record.setCreateTime(new Date());
                    record.setCreateBy(sysUser.getUsername());
                    return record;
                }).collect(Collectors.toList());
            
            companyImportRecordService.saveBatch(records);
            log.info("保存导入错误记录: 单位ID={}, 错误记录数={}", companyRegId, records.size());
        }
        
    } catch (Exception e) {
        log.error("错误记录存储异常，单位ID: {}", companyRegId, e);
    }
    
    // 构造返回结果
    JSONObject batchResult = new JSONObject();
    batchResult.put("successCount", successList.size());
    batchResult.put("failureCount", failureList.size());
    batchResult.put("successResults", successList);
    batchResult.put("failureResults", failureResults);
    
    // 🔥 发布完成事件
    if (isAsync) {
        eventPublisher.publishEvent(ImportProgressEvent.complete(this, taskId, 
            successList.size(), failureList.size(), 
            String.format("导入完成：成功 %d 条，失败 %d 条", successList.size(), failureList.size())));
    }
    
    return Result.OK(batchResult);
}
```

### 4. 修改批量处理方法，添加进度回调

```java
private BatchProcessResult batchProcessCustomerRegsWithProgress(List<CustomerReg> customerRegList, 
                                                               CompanyReg companyReg, 
                                                               List<CompanyTeam> companyTeams, 
                                                               LoginUser sysUser, 
                                                               boolean importWithRateLimit,
                                                               String taskId, 
                                                               boolean isAsync) {
    BatchProcessResult result = new BatchProcessResult();
    
    // 批量检查重复记录
    Map<String, CustomerReg> existingRegsMap = batchCheckExistingRegs(customerRegList, companyReg.getId());
    
    int totalCount = customerRegList.size();
    int processedCount = 0;
    
    // 批量验证和处理
    for (CustomerReg customerReg : customerRegList) {
        try {
            String errorMessage = processCustomerRegRecordWithCache(customerReg, companyReg, companyTeams, sysUser, importWithRateLimit, existingRegsMap);

            if (errorMessage != null) {
                result.addFailure(customerReg, errorMessage);
            } else {
                result.addSuccess(customerReg);
            }
            
            processedCount++;
            
            // 🔥 每处理10条记录发布一次进度事件
            if (isAsync && processedCount % 10 == 0) {
                int progress = (int) ((double) processedCount / totalCount * 80) + 10; // 10-90%的进度
                eventPublisher.publishEvent(new ImportProgressEvent(this, taskId, totalCount, processedCount, 
                    result.getSuccessList().size(), result.getFailureList().size(), 
                    String.format("正在处理第 %d/%d 条记录", processedCount, totalCount)));
            }
            
        } catch (Exception e) {
            log.error("批量处理单条记录失败: {}", customerReg.getName(), e);
            result.addFailure(customerReg, "处理失败: " + e.getMessage());
            processedCount++;
        }
    }
    
    return result;
}
```

## 🚀 **使用方式**

### 前端调用新的异步接口：

```javascript
// 启动异步导入
const response = await fetch('/reg/async-import-v2/import', {
  method: 'POST',
  body: formData // 包含file和companyRegId
});
const taskId = response.result;

// 连接WebSocket接收进度
const socket = new SockJS('/ws/import-progress');
const stompClient = Stomp.over(socket);

stompClient.connect({}, function(frame) {
  // 订阅特定任务的进度
  stompClient.subscribe('/topic/import-progress/' + taskId, function(message) {
    const progress = JSON.parse(message.body);
    console.log('进度更新:', progress);
    
    // 更新UI
    updateProgressUI(progress);
  });
});
```

## 🔑 **核心优势**

1. **最小化修改**: 只在现有方法中添加事件发布
2. **完全解耦**: 通过Spring事件机制分离关注点
3. **实时推送**: WebSocket实时推送进度到前端
4. **向后兼容**: 同步调用时不影响现有功能
5. **易于扩展**: 可以轻松添加更多事件监听器

这种方案比创建新的异步服务类更加优雅和简洁！
