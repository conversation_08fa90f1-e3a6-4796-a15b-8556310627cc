# 后端任务管理逻辑详细分析

## 🏗️ **整体架构**

后端采用了**异步任务 + Redis缓存 + 进度跟踪**的架构模式：

```
用户请求 → 任务创建 → 异步执行 → 进度跟踪 → 结果返回
    ↓           ↓           ↓           ↓           ↓
  Controller → Service → @Async → Redis → WebSocket
```

## 📋 **任务管理流程**

### **1. 任务创建阶段**
```java
@Override
public String startAsyncImport(MultipartFile file, String companyRegId, String username) {
    // 1. 生成唯一任务ID
    String taskId = UUID.randomUUID().toString().replace("-", "");
    
    // 2. 保存上传文件到临时目录
    String filePath = saveUploadedFile(file, taskId);
    
    // 3. 创建任务信息并存储到Redis
    JSONObject taskInfo = new JSONObject();
    taskInfo.put("taskId", taskId);
    taskInfo.put("fileName", file.getOriginalFilename());
    taskInfo.put("fileSize", file.getSize());
    taskInfo.put("companyRegId", companyRegId);
    taskInfo.put("username", username);
    taskInfo.put("status", STATUS_PENDING);
    taskInfo.put("createTime", System.currentTimeMillis());
    
    redisUtil.set(TASK_PREFIX + taskId, taskInfo, 3600); // 1小时过期
    
    // 4. 初始化进度信息
    importProgressService.initProgress(taskId, 0, "任务已创建，开始处理...");
    
    // 5. 异步执行导入任务
    executeAsyncImport(taskId, filePath, companyRegId);
    
    return taskId;
}
```

### **2. 异步执行阶段**
```java
@Async("asyncImportExecutor")  // 使用专门的线程池
public CompletableFuture<Void> executeAsyncImport(String taskId, String filePath, String companyRegId) {
    try {
        // 1. 更新进度状态
        importProgressService.updateProgress(taskId, 0, 0, "正在解析Excel文件...");
        
        // 2. 处理Excel文件
        File file = new File(filePath);
        String result = processExcelFile(file, companyRegId);
        
        // 3. 任务完成处理
        completeTask(taskId, result);
        
    } catch (Exception e) {
        // 4. 任务失败处理
        failTask(taskId, e.getMessage());
    } finally {
        // 5. 清理临时文件
        Files.deleteIfExists(Paths.get(filePath));
    }
    
    return CompletableFuture.completedFuture(null);
}
```

## 📊 **进度跟踪机制**

### **1. 进度信息结构**
```java
public class ImportProgressInfo {
    private String taskId;           // 任务ID
    private int totalCount;          // 总记录数
    private int processedCount;      // 已处理记录数
    private int progress;            // 进度百分比 (0-100)
    private String currentMessage;   // 当前状态消息
    private String status;           // 任务状态
    private long startTime;          // 开始时间
}
```

### **2. 进度初始化**
```java
public void initProgress(String taskId, int totalCount, String message) {
    ImportProgressInfo progressInfo = new ImportProgressInfo();
    progressInfo.setTaskId(taskId);
    progressInfo.setTotalCount(totalCount);
    progressInfo.setProcessedCount(0);
    progressInfo.setProgress(0);
    progressInfo.setCurrentMessage(message);
    progressInfo.setStatus("RUNNING");
    progressInfo.setStartTime(System.currentTimeMillis());

    // 存储到Redis，1小时过期
    String cacheKey = PROGRESS_CACHE_PREFIX + taskId;
    redisUtil.set(cacheKey, progressInfo, 3600);
}
```

### **3. 进度更新**
```java
public void updateProgress(String taskId, int processedCount, int totalCount, String message) {
    ImportProgressInfo progressInfo = getProgress(taskId);
    if (progressInfo == null) {
        // 如果进度信息不存在，先初始化
        initProgress(taskId, totalCount, message);
        progressInfo = getProgress(taskId);
    }

    if (progressInfo != null) {
        progressInfo.setProcessedCount(processedCount);
        progressInfo.setTotalCount(totalCount);
        progressInfo.setCurrentMessage(message);

        // 计算进度百分比
        if (totalCount > 0) {
            int progress = (int) ((double) processedCount / totalCount * 100);
            progressInfo.setProgress(progress);
        }

        // 更新Redis缓存
        String cacheKey = PROGRESS_CACHE_PREFIX + taskId;
        redisUtil.set(cacheKey, progressInfo, 3600);
    }
}
```

### **4. 进度查询**
```java
public ImportProgressInfo getProgress(String taskId) {
    try {
        String cacheKey = PROGRESS_CACHE_PREFIX + taskId;
        Object obj = redisUtil.get(cacheKey);
        if (obj instanceof ImportProgressInfo) {
            return (ImportProgressInfo) obj;
        }
        return null;
    } catch (Exception e) {
        log.error("获取进度信息失败: taskId={}", taskId, e);
        return null;
    }
}
```

## 🗄️ **Redis存储策略**

### **存储结构**
```
Redis Key结构:
├── async_import_task:{taskId}     # 任务基本信息
├── async_import_progress:{taskId} # 进度信息
└── async_import_result:{taskId}   # 结果信息
```

### **数据示例**
```json
// 任务信息 (async_import_task:xxx)
{
  "taskId": "94d6da87aebe4f0f83cae171614ecdd8",
  "fileName": "2025综合利用项目体检人员.xls",
  "fileSize": 48640,
  "companyRegId": "1957297929735770113",
  "username": "admin",
  "status": "RUNNING",
  "createTime": 1756613298253
}

// 进度信息 (async_import_progress:xxx)
{
  "taskId": "94d6da87aebe4f0f83cae171614ecdd8",
  "totalCount": 100,
  "processedCount": 45,
  "progress": 45,
  "currentMessage": "正在处理第45条记录...",
  "status": "RUNNING",
  "startTime": 1756613298253
}

// 结果信息 (async_import_result:xxx)
{
  "taskId": "94d6da87aebe4f0f83cae171614ecdd8",
  "status": "COMPLETED",
  "result": "成功导入100条记录",
  "completeTime": 1756613398253
}
```

## 🔄 **任务状态流转**

```
任务状态流转图:
PENDING → RUNNING → COMPLETED
    ↓         ↓         ↑
    ↓    CANCELLED     ↑
    ↓         ↓         ↑
    └─────→ FAILED ────┘
```

### **状态定义**
- **PENDING**: 任务已创建，等待执行
- **RUNNING**: 任务正在执行中
- **COMPLETED**: 任务执行成功
- **FAILED**: 任务执行失败
- **CANCELLED**: 任务被用户取消

## ⚡ **异步执行机制**

### **线程池配置**
```java
@Configuration
@EnableAsync
public class AsyncImportConfig {
    
    @Bean("asyncImportExecutor")
    public Executor asyncImportExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);      // 核心线程数
        executor.setMaxPoolSize(6);       // 最大线程数
        executor.setQueueCapacity(50);    // 队列容量
        executor.setThreadNamePrefix("AsyncImport-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

### **异步方法**
```java
@Async("asyncImportExecutor")
public CompletableFuture<Void> executeAsyncImport(String taskId, String filePath, String companyRegId) {
    // 异步执行逻辑
    return CompletableFuture.completedFuture(null);
}
```

## 🔍 **监控和日志**

### **关键日志点**
```java
// 任务创建
log.info("异步导入任务已启动，任务ID: {}, 文件: {}, 总耗时: {} ms", taskId, fileName, totalTime);

// 任务执行
log.info("开始执行异步导入任务: {}", taskId);

// 进度更新
log.debug("更新进度: taskId={}, processed={}, total={}, progress={}%", taskId, processed, total, progress);

// 任务完成
log.info("任务完成: {}", taskId);

// 任务失败
log.error("异步导入任务执行失败: {}", taskId, e);
```

### **性能监控**
```java
// 文件保存耗时
long saveStartTime = System.currentTimeMillis();
String filePath = saveUploadedFile(file, taskId);
long saveEndTime = System.currentTimeMillis();
log.info("文件保存完成，耗时: {} ms", saveEndTime - saveStartTime);

// 总体耗时
long endTime = System.currentTimeMillis();
log.info("异步导入任务已启动，总耗时: {} ms", endTime - startTime);
```

## 🎯 **核心特性**

### **1. 高可靠性**
- **异常处理**: 完善的try-catch机制
- **资源清理**: 自动清理临时文件
- **状态一致性**: Redis事务保证数据一致性

### **2. 高性能**
- **异步执行**: 不阻塞用户请求
- **线程池**: 合理的线程池配置
- **缓存策略**: Redis缓存提升查询性能

### **3. 可扩展性**
- **模块化设计**: 清晰的服务分层
- **配置化**: 线程池参数可配置
- **接口标准**: 统一的API接口设计

---

**总结**: 后端采用了成熟的异步任务处理架构，通过Redis实现进度跟踪，具有高可靠性、高性能和良好的可扩展性。
