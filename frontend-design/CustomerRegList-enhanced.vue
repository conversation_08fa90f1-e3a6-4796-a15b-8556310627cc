<template>
  <div>
    <!-- 原有的列表组件内容 -->
    <!-- ... 其他内容保持不变 ... -->
    
    <!-- 导入按钮区域 -->
    <template #action>
      <a-button type="primary" @click="handleImport" preIcon="ant-design:import-outlined">
        导入Excel
      </a-button>
      <a-button type="primary" @click="handleAsyncImport" preIcon="ant-design:cloud-upload-outlined">
        批量导入
      </a-button>
    </template>

    <!-- 异步导入进度弹窗 -->
    <ImportProgressModal
      :visible="progressModalVisible"
      :taskId="currentTaskId"
      :fileInfo="currentFileInfo"
      @cancel="handleCancelImport"
      @retry="handleRetryImport"
      @download-error="handleDownloadError"
      @view-result="handleViewResult"
      @close="handleCloseProgress"
    />

    <!-- 文件上传弹窗 -->
    <a-modal
      v-model:visible="uploadModalVisible"
      title="批量导入Excel"
      :width="600"
      @ok="handleUploadConfirm"
      @cancel="handleUploadCancel"
      :confirmLoading="uploading"
    >
      <div class="upload-container">
        <!-- 单位选择 -->
        <a-form-item label="选择单位" :required="true">
          <a-select
            v-model:value="selectedCompanyRegId"
            placeholder="请选择要导入的单位"
            :options="companyOptions"
            show-search
            :filter-option="filterCompanyOption"
          />
        </a-form-item>

        <!-- 文件上传 -->
        <a-form-item label="选择文件" :required="true">
          <a-upload-dragger
            v-model:fileList="fileList"
            :before-upload="beforeUpload"
            :remove="handleRemoveFile"
            accept=".xlsx,.xls"
            :multiple="false"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持单个文件上传，仅支持 .xlsx 和 .xls 格式，文件大小不超过50MB
            </p>
          </a-upload-dragger>
        </a-form-item>

        <!-- 导入选项 -->
        <a-form-item label="导入选项">
          <a-checkbox-group v-model:value="importOptions">
            <a-checkbox value="skipDuplicate">跳过重复记录</a-checkbox>
            <a-checkbox value="updateExisting">更新已存在记录</a-checkbox>
            <a-checkbox value="validateOnly">仅验证不导入</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <!-- 文件预览信息 -->
        <div v-if="filePreview" class="file-preview">
          <a-descriptions title="文件信息" :column="2" size="small">
            <a-descriptions-item label="文件名">{{ filePreview.fileName }}</a-descriptions-item>
            <a-descriptions-item label="文件大小">{{ formatFileSize(filePreview.fileSize) }}</a-descriptions-item>
            <a-descriptions-item label="预计记录数">{{ filePreview.estimatedRows }}</a-descriptions-item>
            <a-descriptions-item label="预计耗时">{{ filePreview.estimatedTime }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-modal>

    <!-- 导入结果弹窗 -->
    <a-modal
      v-model:visible="resultModalVisible"
      title="导入结果"
      :width="1000"
      :footer="null"
    >
      <ImportResultView
        v-if="importResult"
        :result="importResult"
        @download-error="handleDownloadError"
        @close="resultModalVisible = false"
      />
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import { InboxOutlined } from '@ant-design/icons-vue';
import ImportProgressModal from './components/ImportProgressModal.vue';
import ImportResultView from './components/ImportResultView.vue';
import {
  asyncImportExcel,
  cancelImportTask,
  retryImportTask,
  downloadErrorReport,
  getImportResult,
  type ImportFileParams,
  type ImportResult,
  type ImportTaskInfo,
} from './asyncImport.api';

export default defineComponent({
  name: 'CustomerRegList',
  components: {
    InboxOutlined,
    ImportProgressModal,
    ImportResultView,
  },
  setup() {
    // 异步导入相关状态
    const uploadModalVisible = ref(false);
    const progressModalVisible = ref(false);
    const resultModalVisible = ref(false);
    const uploading = ref(false);
    
    const selectedCompanyRegId = ref('');
    const fileList = ref([]);
    const importOptions = ref(['skipDuplicate']);
    const currentTaskId = ref('');
    const currentFileInfo = ref<ImportTaskInfo | null>(null);
    const importResult = ref<ImportResult | null>(null);
    const filePreview = ref(null);

    // 单位选项（需要从父组件或API获取）
    const companyOptions = ref([
      { label: '示例单位A', value: 'company1' },
      { label: '示例单位B', value: 'company2' },
    ]);

    // 处理异步导入
    const handleAsyncImport = () => {
      uploadModalVisible.value = true;
      selectedCompanyRegId.value = '';
      fileList.value = [];
      importOptions.value = ['skipDuplicate'];
      filePreview.value = null;
    };

    // 文件上传前验证
    const beforeUpload = (file: File) => {
      // 验证文件类型
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel' ||
                     file.name.toLowerCase().endsWith('.xlsx') ||
                     file.name.toLowerCase().endsWith('.xls');
      
      if (!isExcel) {
        message.error('只能上传Excel文件！');
        return false;
      }

      // 验证文件大小（50MB）
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        message.error('文件大小不能超过50MB！');
        return false;
      }

      // 生成文件预览信息
      generateFilePreview(file);
      
      return false; // 阻止自动上传
    };

    // 生成文件预览信息
    const generateFilePreview = (file: File) => {
      filePreview.value = {
        fileName: file.name,
        fileSize: file.size,
        estimatedRows: Math.floor(file.size / 100), // 粗略估算
        estimatedTime: Math.ceil(file.size / 1024 / 1024) + '分钟', // 粗略估算
      };
    };

    // 移除文件
    const handleRemoveFile = () => {
      filePreview.value = null;
      return true;
    };

    // 确认上传
    const handleUploadConfirm = async () => {
      if (!selectedCompanyRegId.value) {
        message.error('请选择要导入的单位');
        return;
      }

      if (fileList.value.length === 0) {
        message.error('请选择要上传的文件');
        return;
      }

      try {
        uploading.value = true;
        
        const file = fileList.value[0].originFileObj || fileList.value[0];
        const params: ImportFileParams = {
          file,
          companyRegId: selectedCompanyRegId.value,
          fileName: file.name,
        };

        // 调用异步导入API
        const response = await asyncImportExcel(params);
        
        if (response.success) {
          currentTaskId.value = response.result;
          currentFileInfo.value = {
            taskId: response.result,
            fileName: file.name,
            fileSize: file.size,
            companyName: getCompanyName(selectedCompanyRegId.value),
            createTime: Date.now(),
          };

          // 关闭上传弹窗，显示进度弹窗
          uploadModalVisible.value = false;
          progressModalVisible.value = true;
          
          message.success('导入任务已创建，正在处理中...');
        } else {
          message.error(response.message || '创建导入任务失败');
        }
      } catch (error) {
        console.error('异步导入失败:', error);
        message.error('创建导入任务失败');
      } finally {
        uploading.value = false;
      }
    };

    // 取消上传
    const handleUploadCancel = () => {
      uploadModalVisible.value = false;
      fileList.value = [];
      filePreview.value = null;
    };

    // 取消导入任务
    const handleCancelImport = async (taskId: string) => {
      try {
        const response = await cancelImportTask(taskId);
        if (response.success) {
          message.success('导入任务已取消');
          progressModalVisible.value = false;
        } else {
          message.error(response.message || '取消任务失败');
        }
      } catch (error) {
        console.error('取消导入任务失败:', error);
        message.error('取消任务失败');
      }
    };

    // 重试导入任务
    const handleRetryImport = async (taskId: string) => {
      try {
        const response = await retryImportTask(taskId);
        if (response.success) {
          currentTaskId.value = response.result;
          message.success('重试任务已创建');
        } else {
          message.error(response.message || '重试任务失败');
        }
      } catch (error) {
        console.error('重试导入任务失败:', error);
        message.error('重试任务失败');
      }
    };

    // 下载错误报告
    const handleDownloadError = async (taskId: string) => {
      try {
        const response = await downloadErrorReport(taskId);
        
        // 创建下载链接
        const blob = new Blob([response], { 
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `导入错误报告_${taskId}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        message.success('错误报告下载成功');
      } catch (error) {
        console.error('下载错误报告失败:', error);
        message.error('下载错误报告失败');
      }
    };

    // 查看导入结果
    const handleViewResult = async (taskId: string) => {
      try {
        const response = await getImportResult(taskId);
        if (response.success) {
          importResult.value = response.result;
          resultModalVisible.value = true;
          progressModalVisible.value = false;
        } else {
          message.error(response.message || '获取导入结果失败');
        }
      } catch (error) {
        console.error('获取导入结果失败:', error);
        message.error('获取导入结果失败');
      }
    };

    // 关闭进度弹窗
    const handleCloseProgress = () => {
      progressModalVisible.value = false;
      currentTaskId.value = '';
      currentFileInfo.value = null;
    };

    // 工具函数
    const formatFileSize = (size: number) => {
      if (!size) return '';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return `${size.toFixed(1)} ${units[index]}`;
    };

    const getCompanyName = (companyRegId: string) => {
      const company = companyOptions.value.find(item => item.value === companyRegId);
      return company ? company.label : '未知单位';
    };

    const filterCompanyOption = (input: string, option: any) => {
      return option.label.toLowerCase().includes(input.toLowerCase());
    };

    // 传统导入处理（保持原有逻辑）
    const handleImport = () => {
      // 原有的同步导入逻辑
      console.log('传统导入');
    };

    return {
      // 异步导入相关
      uploadModalVisible,
      progressModalVisible,
      resultModalVisible,
      uploading,
      selectedCompanyRegId,
      fileList,
      importOptions,
      currentTaskId,
      currentFileInfo,
      importResult,
      filePreview,
      companyOptions,
      
      // 方法
      handleAsyncImport,
      handleImport,
      beforeUpload,
      handleRemoveFile,
      handleUploadConfirm,
      handleUploadCancel,
      handleCancelImport,
      handleRetryImport,
      handleDownloadError,
      handleViewResult,
      handleCloseProgress,
      formatFileSize,
      filterCompanyOption,
    };
  },
});
</script>

<style scoped>
.upload-container {
  padding: 16px 0;
}

.file-preview {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

.ant-upload-text {
  font-size: 16px;
  color: #666;
}

.ant-upload-hint {
  font-size: 14px;
  color: #999;
}
</style>
