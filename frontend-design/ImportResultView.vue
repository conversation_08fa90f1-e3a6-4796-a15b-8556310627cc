<template>
  <div class="import-result-container">
    <!-- 结果概览 -->
    <div class="result-summary">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="总记录数"
            :value="result.totalCount"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="成功导入"
            :value="result.successCount"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="导入失败"
            :value="result.failureCount"
            :value-style="{ color: '#ff4d4f' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="成功率"
            :value="successRate"
            suffix="%"
            :value-style="{ color: successRate >= 90 ? '#52c41a' : successRate >= 70 ? '#faad14' : '#ff4d4f' }"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 导入信息 -->
    <div class="import-info">
      <a-descriptions title="导入信息" :column="3" size="small">
        <a-descriptions-item label="文件名">{{ result.summary.fileName }}</a-descriptions-item>
        <a-descriptions-item label="文件大小">{{ formatFileSize(result.summary.fileSize) }}</a-descriptions-item>
        <a-descriptions-item label="单位名称">{{ result.summary.companyName }}</a-descriptions-item>
        <a-descriptions-item label="开始时间">{{ formatTime(result.summary.startTime) }}</a-descriptions-item>
        <a-descriptions-item label="结束时间">{{ formatTime(result.summary.endTime) }}</a-descriptions-item>
        <a-descriptions-item label="耗时">{{ formatDuration(result.summary.duration) }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button v-if="result.failureCount > 0" @click="handleDownloadError" type="primary" danger>
          <template #icon><download-outlined /></template>
          下载错误报告
        </a-button>
        <a-button @click="handleExportSuccess" type="primary">
          <template #icon><export-outlined /></template>
          导出成功记录
        </a-button>
        <a-button @click="handleClose">
          关闭
        </a-button>
      </a-space>
    </div>

    <!-- 详细结果标签页 -->
    <div class="result-details">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 成功记录 -->
        <a-tab-pane key="success" :tab="`成功记录 (${result.successCount})`">
          <a-table
            :columns="successColumns"
            :data-source="result.successList"
            :pagination="successPagination"
            size="small"
            :scroll="{ x: 1200, y: 400 }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (successPagination.current - 1) * successPagination.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag color="success">成功</a-tag>
              </template>
              <template v-else-if="column.key === 'examNo'">
                <a-button type="link" size="small" @click="handleViewDetail(record)">
                  {{ record.examNo }}
                </a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 失败记录 -->
        <a-tab-pane key="failure" :tab="`失败记录 (${result.failureCount})`">
          <a-table
            :columns="failureColumns"
            :data-source="result.failureList"
            :pagination="failurePagination"
            size="small"
            :scroll="{ x: 1400, y: 400 }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (failurePagination.current - 1) * failurePagination.pageSize + index + 1 }}
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag color="error">失败</a-tag>
              </template>
              <template v-else-if="column.key === 'reason'">
                <a-tooltip :title="record.reason">
                  <span class="error-reason">{{ record.reason }}</span>
                </a-tooltip>
              </template>
              <template v-else-if="column.key === 'rowIndex'">
                第{{ record.rowIndex }}行
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 错误统计 -->
        <a-tab-pane key="errorStats" tab="错误统计" v-if="result.failureCount > 0">
          <div class="error-stats">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="错误类型分布" size="small">
                  <div class="error-chart">
                    <!-- 这里可以集成图表组件显示错误类型分布 -->
                    <div v-for="(count, errorType) in errorTypeStats" :key="errorType" class="error-item">
                      <span class="error-type">{{ errorType }}</span>
                      <span class="error-count">{{ count }}条</span>
                      <a-progress 
                        :percent="(count / result.failureCount) * 100" 
                        size="small" 
                        :show-info="false"
                      />
                    </div>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="常见错误" size="small">
                  <a-list size="small" :data-source="commonErrors">
                    <template #renderItem="{ item }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #title>{{ item.error }}</template>
                          <template #description>出现{{ item.count }}次</template>
                        </a-list-item-meta>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, PropType } from 'vue';
import { DownloadOutlined, ExportOutlined } from '@ant-design/icons-vue';
import type { ImportResult } from '../asyncImport.api';

export default defineComponent({
  name: 'ImportResultView',
  components: {
    DownloadOutlined,
    ExportOutlined,
  },
  props: {
    result: {
      type: Object as PropType<ImportResult>,
      required: true,
    },
  },
  emits: ['download-error', 'export-success', 'close'],
  setup(props, { emit }) {
    const activeTab = ref('success');

    // 成功率计算
    const successRate = computed(() => {
      if (props.result.totalCount === 0) return 0;
      return Math.round((props.result.successCount / props.result.totalCount) * 100);
    });

    // 成功记录表格配置
    const successColumns = [
      { title: '序号', key: 'index', width: 80, fixed: 'left' },
      { title: '状态', key: 'status', width: 80 },
      { title: '体检号', key: 'examNo', width: 120 },
      { title: '姓名', dataIndex: 'name', width: 100 },
      { title: '身份证号', dataIndex: 'idCard', width: 180 },
      { title: '性别', dataIndex: 'gender', width: 80 },
      { title: '年龄', dataIndex: 'age', width: 80 },
      { title: '分组', dataIndex: 'teamName', width: 120 },
      { title: '创建时间', dataIndex: 'createTime', width: 150 },
    ];

    const successPagination = ref({
      current: 1,
      pageSize: 10,
      total: props.result.successCount,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条记录`,
    });

    // 失败记录表格配置
    const failureColumns = [
      { title: '序号', key: 'index', width: 80, fixed: 'left' },
      { title: '状态', key: 'status', width: 80 },
      { title: '行号', key: 'rowIndex', width: 80 },
      { title: '姓名', dataIndex: ['item', 'name'], width: 100 },
      { title: '身份证号', dataIndex: ['item', 'idCard'], width: 180 },
      { title: '性别', dataIndex: ['item', 'gender'], width: 80 },
      { title: '年龄', dataIndex: ['item', 'age'], width: 80 },
      { title: '分组', dataIndex: ['item', 'teamName'], width: 120 },
      { title: '错误原因', key: 'reason', width: 200, ellipsis: true },
    ];

    const failurePagination = ref({
      current: 1,
      pageSize: 10,
      total: props.result.failureCount,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条记录`,
    });

    // 错误类型统计
    const errorTypeStats = computed(() => {
      const stats: Record<string, number> = {};
      props.result.failureList.forEach(item => {
        const errorType = getErrorType(item.reason);
        stats[errorType] = (stats[errorType] || 0) + 1;
      });
      return stats;
    });

    // 常见错误
    const commonErrors = computed(() => {
      const errorCounts: Record<string, number> = {};
      props.result.failureList.forEach(item => {
        errorCounts[item.reason] = (errorCounts[item.reason] || 0) + 1;
      });
      
      return Object.entries(errorCounts)
        .map(([error, count]) => ({ error, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);
    });

    // 工具函数
    const formatFileSize = (size: number) => {
      if (!size) return '';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return `${size.toFixed(1)} ${units[index]}`;
    };

    const formatTime = (timestamp: number) => {
      if (!timestamp) return '';
      return new Date(timestamp).toLocaleString();
    };

    const formatDuration = (duration: number) => {
      if (!duration) return '';
      const seconds = Math.floor(duration / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      
      if (hours > 0) {
        return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`;
      } else {
        return `${seconds}秒`;
      }
    };

    const getErrorType = (reason: string) => {
      if (reason.includes('身份证')) return '身份证错误';
      if (reason.includes('分组')) return '分组错误';
      if (reason.includes('重复')) return '重复记录';
      if (reason.includes('格式')) return '格式错误';
      if (reason.includes('必填')) return '必填项缺失';
      return '其他错误';
    };

    // 事件处理
    const handleDownloadError = () => {
      emit('download-error', props.result.taskId);
    };

    const handleExportSuccess = () => {
      emit('export-success', props.result.taskId);
    };

    const handleClose = () => {
      emit('close');
    };

    const handleViewDetail = (record: any) => {
      // 查看详细信息
      console.log('查看详情:', record);
    };

    return {
      activeTab,
      successRate,
      successColumns,
      successPagination,
      failureColumns,
      failurePagination,
      errorTypeStats,
      commonErrors,
      formatFileSize,
      formatTime,
      formatDuration,
      handleDownloadError,
      handleExportSuccess,
      handleClose,
      handleViewDetail,
    };
  },
});
</script>

<style scoped>
.import-result-container {
  padding: 16px 0;
}

.result-summary {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.import-info {
  margin-bottom: 24px;
}

.action-buttons {
  margin-bottom: 24px;
  text-align: right;
}

.result-details {
  margin-top: 16px;
}

.error-reason {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.error-stats {
  padding: 16px 0;
}

.error-chart {
  max-height: 300px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.error-type {
  flex: 1;
  font-weight: 500;
}

.error-count {
  width: 60px;
  text-align: right;
  margin-right: 12px;
  color: #666;
}
</style>
