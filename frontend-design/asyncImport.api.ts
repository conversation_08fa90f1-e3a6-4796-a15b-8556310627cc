import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';

enum Api {
  // 异步导入相关接口
  asyncImportExcel = '/reg/async-import/excel',
  getImportProgress = '/reg/async-import/progress',
  cancelImportTask = '/reg/async-import/cancel',
  getImportResult = '/reg/async-import/result',
  downloadErrorReport = '/reg/async-import/error-report',
  retryImportTask = '/reg/async-import/retry',
}

/**
 * 异步导入Excel
 * @param params 上传参数
 */
export const asyncImportExcel = (params: UploadFileParams & { companyRegId: string }) => {
  return defHttp.uploadFile<{ data: string; message: string }>(
    {
      url: Api.asyncImportExcel,
    },
    params,
  );
};

/**
 * 获取导入进度
 * @param taskId 任务ID
 */
export const getImportProgress = (taskId: string) => {
  return defHttp.get<{
    taskId: string;
    status: string;
    progress: number;
    totalCount: number;
    processedCount: number;
    successCount: number;
    failureCount: number;
    currentMessage: string;
    errorMessage: string;
    startTime: number;
    endTime: number;
    duration: number;
  }>({
    url: `${Api.getImportProgress}/${taskId}`,
  });
};

/**
 * 取消导入任务
 * @param taskId 任务ID
 */
export const cancelImportTask = (taskId: string) => {
  return defHttp.post<{ message: string }>({
    url: `${Api.cancelImportTask}/${taskId}`,
  });
};

/**
 * 获取导入结果详情
 * @param taskId 任务ID
 */
export const getImportResult = (taskId: string) => {
  return defHttp.get<{
    taskId: string;
    status: string;
    totalCount: number;
    successCount: number;
    failureCount: number;
    successList: any[];
    failureList: any[];
    summary: any;
  }>({
    url: `${Api.getImportResult}/${taskId}`,
  });
};

/**
 * 下载错误报告
 * @param taskId 任务ID
 */
export const downloadErrorReport = (taskId: string) => {
  return defHttp.get(
    {
      url: `${Api.downloadErrorReport}/${taskId}`,
      responseType: 'blob',
    },
    { isTransformResponse: false }
  );
};

/**
 * 重试导入任务
 * @param taskId 原任务ID
 */
export const retryImportTask = (taskId: string) => {
  return defHttp.post<{ data: string; message: string }>({
    url: `${Api.retryImportTask}/${taskId}`,
  });
};

/**
 * WebSocket连接URL
 */
export const getWebSocketUrl = (taskId: string) => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = window.location.host;
  return `${protocol}//${host}/websocket/import-progress?taskId=${taskId}`;
};

/**
 * 导入状态枚举
 */
export enum ImportStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

/**
 * 导入进度信息接口
 */
export interface ImportProgressInfo {
  taskId: string;
  status: ImportStatus;
  progress: number;
  totalCount: number;
  processedCount: number;
  successCount: number;
  failureCount: number;
  currentMessage: string;
  errorMessage?: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

/**
 * 导入结果接口
 */
export interface ImportResult {
  taskId: string;
  status: ImportStatus;
  totalCount: number;
  successCount: number;
  failureCount: number;
  successList: any[];
  failureList: Array<{
    item: any;
    reason: string;
    rowIndex: number;
  }>;
  summary: {
    fileName: string;
    fileSize: number;
    companyName: string;
    startTime: number;
    endTime: number;
    duration: number;
  };
}

/**
 * 文件上传参数接口
 */
export interface ImportFileParams {
  file: File;
  companyRegId: string;
  fileName?: string;
}

/**
 * 导入任务信息接口
 */
export interface ImportTaskInfo {
  taskId: string;
  fileName: string;
  fileSize: number;
  companyName: string;
  createTime: number;
}
