<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    :width="800"
    :closable="!isProcessing"
    :maskClosable="false"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="import-progress-container">
      <!-- 文件信息 -->
      <div class="file-info" v-if="fileInfo">
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="文件名">{{ fileInfo.fileName }}</a-descriptions-item>
          <a-descriptions-item label="文件大小">{{ formatFileSize(fileInfo.fileSize) }}</a-descriptions-item>
          <a-descriptions-item label="单位名称">{{ fileInfo.companyName }}</a-descriptions-item>
          <a-descriptions-item label="开始时间">{{ formatTime(progressInfo.startTime) }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 进度条 -->
      <div class="progress-section">
        <a-progress
          :percent="progressInfo.progress || 0"
          :status="getProgressStatus()"
          :stroke-color="getProgressColor()"
          :show-info="true"
        />
        
        <!-- 详细进度信息 -->
        <div class="progress-details" v-if="progressInfo.totalCount > 0">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总记录数" :value="progressInfo.totalCount" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="已处理" :value="progressInfo.processedCount || 0" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="成功" :value="progressInfo.successCount || 0" value-style="color: #52c41a" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="失败" :value="progressInfo.failureCount || 0" value-style="color: #ff4d4f" />
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 当前状态 -->
      <div class="status-section">
        <a-alert
          :message="getStatusMessage()"
          :description="progressInfo.currentMessage"
          :type="getAlertType()"
          :show-icon="true"
        />
      </div>

      <!-- 错误信息 -->
      <div class="error-section" v-if="progressInfo.errorMessage">
        <a-alert
          message="错误信息"
          :description="progressInfo.errorMessage"
          type="error"
          :show-icon="true"
        />
      </div>

      <!-- 实时日志 -->
      <div class="log-section" v-if="showLogs">
        <a-card title="实时日志" size="small">
          <div class="log-content" ref="logContainer">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <a-space>
          <a-button v-if="canCancel" @click="handleCancel" danger>
            取消导入
          </a-button>
          <a-button v-if="canRetry" @click="handleRetry" type="primary">
            重新导入
          </a-button>
          <a-button v-if="canDownloadError" @click="handleDownloadError">
            下载错误报告
          </a-button>
          <a-button v-if="canViewResult" @click="handleViewResult" type="primary">
            查看结果
          </a-button>
          <a-button v-if="isCompleted" @click="handleClose">
            关闭
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';

export default {
  name: 'ImportProgressModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: String,
      default: ''
    },
    fileInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      progressInfo: {
        status: 'PENDING',
        progress: 0,
        totalCount: 0,
        processedCount: 0,
        successCount: 0,
        failureCount: 0,
        currentMessage: '',
        errorMessage: '',
        startTime: null,
        endTime: null,
        duration: null
      },
      logs: [],
      showLogs: false,
      websocket: null,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5
    };
  },
  computed: {
    modalTitle() {
      return `导入进度 - ${this.fileInfo.fileName || ''}`;
    },
    isProcessing() {
      return ['PENDING', 'PROCESSING'].includes(this.progressInfo.status);
    },
    isCompleted() {
      return this.progressInfo.status === 'COMPLETED';
    },
    isFailed() {
      return this.progressInfo.status === 'FAILED';
    },
    canCancel() {
      return this.isProcessing;
    },
    canRetry() {
      return this.isFailed;
    },
    canDownloadError() {
      return this.isCompleted && this.progressInfo.failureCount > 0;
    },
    canViewResult() {
      return this.isCompleted;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.taskId) {
        this.initWebSocket();
      } else {
        this.closeWebSocket();
      }
    },
    taskId(newVal) {
      if (newVal && this.visible) {
        this.initWebSocket();
      }
    }
  },
  methods: {
    initWebSocket() {
      if (this.websocket) {
        this.closeWebSocket();
      }

      const wsUrl = `ws://localhost:8080/websocket/import-progress?taskId=${this.taskId}`;
      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = () => {
        console.log('WebSocket连接已建立');
        this.reconnectAttempts = 0;
      };

      this.websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.updateProgress(data);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      this.websocket.onclose = () => {
        console.log('WebSocket连接已关闭');
        if (this.visible && this.isProcessing && this.reconnectAttempts < this.maxReconnectAttempts) {
          setTimeout(() => {
            this.reconnectAttempts++;
            this.initWebSocket();
          }, 2000);
        }
      };

      this.websocket.onerror = (error) => {
        console.error('WebSocket连接错误:', error);
      };
    },

    closeWebSocket() {
      if (this.websocket) {
        this.websocket.close();
        this.websocket = null;
      }
    },

    updateProgress(data) {
      this.progressInfo = { ...this.progressInfo, ...data };
      
      // 添加日志
      if (data.currentMessage) {
        this.addLog(data.currentMessage);
      }

      // 滚动到日志底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    addLog(message) {
      this.logs.push({
        timestamp: Date.now(),
        message: message
      });
      
      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(-100);
      }
    },

    scrollToBottom() {
      if (this.$refs.logContainer) {
        this.$refs.logContainer.scrollTop = this.$refs.logContainer.scrollHeight;
      }
    },

    getProgressStatus() {
      switch (this.progressInfo.status) {
        case 'PROCESSING':
          return 'active';
        case 'COMPLETED':
          return 'success';
        case 'FAILED':
          return 'exception';
        default:
          return 'normal';
      }
    },

    getProgressColor() {
      switch (this.progressInfo.status) {
        case 'PROCESSING':
          return '#1890ff';
        case 'COMPLETED':
          return '#52c41a';
        case 'FAILED':
          return '#ff4d4f';
        default:
          return '#d9d9d9';
      }
    },

    getStatusMessage() {
      switch (this.progressInfo.status) {
        case 'PENDING':
          return '等待处理';
        case 'PROCESSING':
          return '正在处理';
        case 'COMPLETED':
          return '处理完成';
        case 'FAILED':
          return '处理失败';
        case 'CANCELLED':
          return '已取消';
        default:
          return '未知状态';
      }
    },

    getAlertType() {
      switch (this.progressInfo.status) {
        case 'PROCESSING':
          return 'info';
        case 'COMPLETED':
          return 'success';
        case 'FAILED':
          return 'error';
        default:
          return 'info';
      }
    },

    formatFileSize(size) {
      if (!size) return '';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return `${size.toFixed(1)} ${units[index]}`;
    },

    formatTime(timestamp) {
      if (!timestamp) return '';
      return new Date(timestamp).toLocaleString();
    },

    handleCancel() {
      if (this.isProcessing) {
        this.$emit('cancel', this.taskId);
      } else {
        this.handleClose();
      }
    },

    handleRetry() {
      this.$emit('retry', this.taskId);
    },

    handleDownloadError() {
      this.$emit('download-error', this.taskId);
    },

    handleViewResult() {
      this.$emit('view-result', this.taskId);
    },

    handleClose() {
      this.closeWebSocket();
      this.$emit('close');
    }
  },

  beforeDestroy() {
    this.closeWebSocket();
  }
};
</script>

<style scoped>
.import-progress-container {
  padding: 16px 0;
}

.file-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.progress-section {
  margin-bottom: 24px;
}

.progress-details {
  margin-top: 16px;
}

.status-section {
  margin-bottom: 16px;
}

.error-section {
  margin-bottom: 16px;
}

.log-section {
  margin-bottom: 24px;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-message {
  color: #333;
}

.action-section {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>
